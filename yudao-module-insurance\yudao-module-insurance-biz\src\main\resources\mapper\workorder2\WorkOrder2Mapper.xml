<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.insurance.dal.mysql.workorder2.WorkOrder2Mapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPage2" resultType="cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrder2DO">
        select iwo2.*
        from insurance_work_order2 as iwo2
        left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
        <where>
            iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
            <if test="reqVO.hospitalCode != null and reqVO.hospitalCode != ''"> and iwo2.hospital_code = #{reqVO.hospitalCode}</if>
            <if test="reqVO.hospitalName != null and reqVO.hospitalName != ''"> and iwo2.hospital_name = #{reqVO.hospitalName}</if>
            <if test="reqVO.treatmentSerialNumberType != null"> and iwo2.treatment_serial_number_type = #{reqVO.treatmentSerialNumberType}</if>
            <if test="reqVO.treatmentSerialNumber != null and reqVO.treatmentSerialNumber != ''"> and iwo2.treatment_serial_number = #{reqVO.treatmentSerialNumber}</if>
            <if test="reqVO.name != null and reqVO.name != ''"> and iwo2.name = #{reqVO.name}</if>
            <if test="reqVO.idCardNumber != null and reqVO.idCardNumber != ''"> and iwo2.id_card_number = #{reqVO.idCardNumber}</if>
            <if test="reqVO.mobilePhoneNumber != null and reqVO.mobilePhoneNumber != ''"> and iwo2.mobile_phone_number = #{reqVO.mobilePhoneNumber}</if>
            <if test="reqVO.socialMedicareCardNumber != null and reqVO.socialMedicareCardNumber != ''"> and iwo2.social_medicare_card_number = #{reqVO.socialMedicareCardNumber}</if>
            <if test="reqVO.status != null"> and iwo2.status = #{reqVO.status}</if>
            <if test="reqVO.beginTreatmentDatetime != null"><![CDATA[ and iwo2.treatment_datetime >= #{reqVO.beginTreatmentDatetime}]]></if>
            <if test="reqVO.endTreatmentDatetime != null"><![CDATA[ and iwo2.treatment_datetime < #{reqVO.endTreatmentDatetime}]]></if>
        </where>
        order by iwo2.id desc
    </select>

    <select id="countCompensatingMoney" resultType="java.math.BigDecimal">
        select sum(iwo2.actual_money)
        from insurance_work_order2 as iwo2
        left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
        where iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countCompensatingMoneyGroupByCompany" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.InsuranceCompensatingMoney">
        select ic.id as company_id, ic.name as company_name, ifnull(sum(iwo.actual_money), 0) as compensating_money
        from insurance_company as ic
        left join insurance_area_company as iac on ic.id = iac.company_id
        left join insurance_work_order2 as iwo on iwo.area_id = iac.area_id and iwo.deleted = 0
        left join insurance_auth as ia on iwo.id_card_number = ia.idcard
        where (ia.id is not null or iwo.status = 4)
        group by ic.id, ic.name
    </select>

    <select id="countCompensatingPersonNumberGroupByMonth" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.CompensatingMonthPersonNumber">
        select t.m as `month`, sum(if(isnull(iwo.id), 0, 1)) as person_number
        from
        (
            select 1 as m
            union all
            select 2 as m
            union all
            select 3 as m
            union all
            select 4 as m
            union all
            select 5 as m
            union all
            select 6 as m
            union all
            select 7 as m
            union all
            select 8 as m
            union all
            select 9 as m
            union all
            select 10 as m
            union all
            select 11 as m
            union all
            select 12 as m
        ) as t
        left join
        (
            select iwo2.*
            from insurance_work_order2 as iwo2
            left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
            where iwo2.status = 4 and date_format(iwo2.compensating_date, '%Y') = #{year} and iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
        ) as iwo on t.m = date_format(iwo.compensating_date, '%m')
        group by t.m
        order by t.m
    </select>

    <select id="countCompensatingPersonNumberGroupByArea" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.CompensatingAreaPersonNumber">
        select ia.id as area_id, ia.name as area_name, sum(if(isnull(iwo.id), 0, 1)) as person_number
        from insurance_area as ia
        left join
        (
            select iwo2.*
            from insurance_work_order2 as iwo2
            left join insurance_auth as iauth on iwo2.id_card_number = iauth.idcard
            where iwo2.status = 4 and date_format(iwo2.compensating_date, '%Y') = #{year} and iwo2.deleted = 0 and (iauth.id is not null or iwo2.status = 4)
        ) as iwo on ia.id = iwo.area_id
        group by ia.id, ia.name
    </select>
    <select id="selectWorkOrderPage"
            resultType="cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.WorkOrder2RespVO">
        select ic.name as company_name, iwop.signed_pdf as `pdf.signed_pdf`, iwop.unsigned_pdf as `pdf.unsigned_pdf`, iwo2.id,
        iwo2.type,
        iwo2.treatment_serial_number_type,
        iwo2.name,
        iwo2.id_card_number,
        iwo2.mobile_phone_number,
        iwo2.hospital_name,
        iwo2.treatment_datetime,
        iwo2.suggest_compensating_money,
        iwo2.actual_money,
        iwo2.complete_status,
        iwo2.create_time,
        iwo2.status,
        iwo2.area_id, iwoe.create_time as end_date, case when ia.id is null then '未授权' else '已授权' end as auth_status,
        iwo2.supplementary_file_record_id
        from insurance_work_order2 as iwo2
        left join insurance_work_order_pdf as iwop on  iwop.order_id = iwo2.id
        left join insurance_area_company as iac on iwo2.area_id = iac.area_id
        LEFT JOIN insurance_company as ic on iac.company_id = ic.id
        left join insurance_work_order_event as iwoe on iwoe.work_order_id = iwo2.id and iwoe.`type` = 4
        left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
        <where>
            iac.agreement_date &lt;= iwo2.treatment_datetime and iac.agreement_end_date > iwo2.treatment_datetime and JSON_CONTAINS(iac.types, concat('[', iwo2.type, ']')) and iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
            <if test="reqVO.hospitalCode != null and reqVO.hospitalCode != ''"> and iwo2.hospital_code = #{reqVO.hospitalCode}</if>
            <if test="reqVO.hospitalName != null and reqVO.hospitalName != ''"> and iwo2.hospital_name = #{reqVO.hospitalName}</if>
            <if test="reqVO.treatmentSerialNumberType != null"> and iwo2.treatment_serial_number_type = #{reqVO.treatmentSerialNumberType}</if>
            <if test="reqVO.treatmentSerialNumber != null and reqVO.treatmentSerialNumber != ''"> and iwo2.treatment_serial_number = #{reqVO.treatmentSerialNumber}</if>
            <if test="reqVO.name != null and reqVO.name != ''"> and iwo2.name = #{reqVO.name}</if>
            <if test="reqVO.idCardNumber != null and reqVO.idCardNumber != ''"> and iwo2.id_card_number = #{reqVO.idCardNumber}</if>
            <if test="reqVO.mobilePhoneNumber != null and reqVO.mobilePhoneNumber != ''"> and iwo2.mobile_phone_number = #{reqVO.mobilePhoneNumber}</if>
            <if test="reqVO.socialMedicareCardNumber != null and reqVO.socialMedicareCardNumber != ''"> and iwo2.social_medicare_card_number = #{reqVO.socialMedicareCardNumber}</if>
            <if test="reqVO.status != null"> and iwo2.status = #{reqVO.status}</if>
            <if test="reqVO.completeStatus != null and reqVO.completeStatus != ''"> and iwo2.complete_status = #{reqVO.completeStatus}</if>
            <if test="reqVO.type != null"> and iwo2.type = #{reqVO.type}</if>
            <if test="reqVO.types != null and reqVO.types.size() &gt; 0">
                and iwo2.type in
                <foreach collection="reqVO.types" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="not (reqVO.types != null and reqVO.types.size() &gt; 0)">
                and 1 = 2
            </if>
            <if test="reqVO.beginCreateTime != null"><![CDATA[ and iwo2.create_time >= #{reqVO.beginCreateTime}]]></if>
            <if test="reqVO.endCreateTime != null"><![CDATA[ and iwo2.create_time < #{reqVO.endCreateTime}]]></if>
        </where>
        order by iwo2.create_time desc
    </select>

    <select id="selectCompensatingPersonAndCompanyPage" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.CompensatingPersonAndCompany">
        select iwo2.id, iwo2.name, iwo2.id_card_number, ic.name as company_name, iwo2.compensating_date, iwo2.actual_money as compensating_money, iwoe.create_time as end_date
        from insurance_work_order2 as iwo2
        left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
        left join insurance_area_company as iac on iwo2.area_id = iac.area_id
        left join insurance_company as ic on ic.id = iac.company_id
        left join insurance_work_order_event as iwoe on iwoe.work_order_id = iwo2.id and iwoe.`type` = 4
        where iwo2.status = #{reqVO.status} and iac.agreement_date &lt;= iwo2.treatment_datetime and iac.agreement_end_date > iwo2.treatment_datetime and JSON_CONTAINS(iac.types, concat('[', iwo2.type, ']')) and iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
        order by iwoe.create_time desc
    </select>

    <select id="countSuggestCompensatingMoney" resultType="java.math.BigDecimal">
        select sum(iwo2.suggest_compensating_money)
        from insurance_work_order2 as iwo2
        left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
        where iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countWorkOrderGroupByArea" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.WorkOrderStatisticsByArea">
        select ia.id as area_id,
                ia.name as area_name,
                sum(if(isnull(iwo.id), 0, 1)) as person_number,
                ifnull(sum(iwo.suggest_compensating_money), 0) as suggest_compensating_money,
                ifnull(sum(iwo.actual_money), 0) as actual_money
        from insurance_area as ia
        left join
        (
            select iwo2.*
            from insurance_work_order2 as iwo2
            left join insurance_auth as iauth on iwo2.id_card_number = iauth.idcard
            where iwo2.compensating_date is not null and iwo2.deleted = 0 and (iauth.id is not null or iwo2.status = 4)
        ) as iwo on ia.id = iwo.area_id
        group by ia.id, ia.name
    </select>

    <select id="countWorkOrderGroupByAge" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.WorkOrderStatisticsByAge">
        select concat(#{beginAge}, '-', #{endAge}) as age_range,
                sum(if(isnull(iwo2.id), 0, 1)) as person_number,
                ifnull(sum(iwo2.suggest_compensating_money), 0) as suggest_compensating_money,
                ifnull(sum(iwo2.actual_money), 0) as actual_money
        from insurance_work_order2 as iwo2
        left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
        where iwo2.age between #{beginAge} and #{endAge} and iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countWorkOrderByDiagnosisCodeAndAge" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.WorkOrderStatisticsByDiagnosisCodeAndAge">
        select concat(#{beginAge}, '-', #{endAge}) as age_range,
                ifnull(sum(if(isnull(iwo2.id), 0, 1)), 0) as person_number,
                ifnull(sum(iwo2.suggest_compensating_money), 0) as suggest_compensating_money,
                ifnull(sum(iwo2.actual_money), 0) as actual_money
        from insurance_work_order2 as iwo2
        left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
        where iwo2.age between #{beginAge} and #{endAge} and find_in_set(#{diagnosisCode}, concat_ws(',', iwo2.main_diagnosis_code , iwo2.other_diagnosis_code)) > 0 and iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countWorkOrderByDiagnosisCodeGroupByArea" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.WorkOrderStatisticsByDiagnosisCodeAndArea">
        select ia.id as area_id,
                ia.name as area_name,
                ifnull(sum(if(isnull(iwo.id), 0, 1)), 0) as person_number,
                ifnull(sum(iwo.suggest_compensating_money), 0) as suggest_compensating_money,
                ifnull(sum(actual_money), 0) as actual_money
        from insurance_area as ia
        left join
        (
            select iwo2.*
            from insurance_work_order2 as iwo2
            left join insurance_auth as iauth on iwo2.id_card_number = iauth.idcard
            where find_in_set(#{diagnosisCode}, concat_ws(',', iwo2.main_diagnosis_code , iwo2.other_diagnosis_code)) > 0 and iwo2.deleted = 0 and (iauth.id is not null or iwo2.status = 4)
        ) as iwo on ia.id = iwo.area_id
        group by ia.id, ia.name
    </select>

    <select id="countWorkOrderByDiagnosisCodeGroupByMonth" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.WorkOrderStatisticsByDiagnosisCodeAndMonth">
        select t.m as `month`,
                ifnull(sum(if(isnull(iwo.id), 0, 1)), 0) as person_number,
                ifnull(sum(iwo.suggest_compensating_money), 0) as suggest_compensating_money,
                ifnull(sum(actual_money), 0) as actual_money
        from
        (
            select 1 as m
            union all
            select 2 as m
            union all
            select 3 as m
            union all
            select 4 as m
            union all
            select 5 as m
            union all
            select 6 as m
            union all
            select 7 as m
            union all
            select 8 as m
            union all
            select 9 as m
            union all
            select 10 as m
            union all
            select 11 as m
            union all
            select 12 as m
        ) as t
        left join
        (
            select iwo2.*
            from insurance_work_order2 as iwo2
            left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
            where date_format(iwo2.create_time, '%Y') = #{year} and find_in_set(#{diagnosisCode}, concat_ws(',', iwo2.main_diagnosis_code , iwo2.other_diagnosis_code)) > 0 and iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
        ) as iwo on t.m = date_format(iwo.create_time, '%m')
        group by t.m
        order by t.m
    </select>

    <select id="countWorkOrderByDiagnosisCodeGroupBySex" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.WorkOrderStatisticsByDiagnosisCodeAndSex">
        select iwo2.gender as sex,
                ifnull(sum(if(isnull(iwo2.id), 0, 1)), 0) as person_number,
                ifnull(sum(iwo2.suggest_compensating_money), 0) as suggest_compensating_money,
                ifnull(sum(iwo2.actual_money), 0) as actual_money
        from insurance_work_order2 as iwo2
        left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
        where find_in_set(#{diagnosisCode}, concat_ws(',', iwo2.main_diagnosis_code , iwo2.other_diagnosis_code)) > 0 and iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
        group by iwo2.gender
    </select>

    <select id="countTopDiagnosis" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.TopDiagnosis">
        select t3.diagnosis_code,
        (select icd_name from insurance_diagnosis where icd = t3.diagnosis_code) as diagnosis_name,
        count(t3.id) as `count`
        from (
            select distinct t2.*
            from (
                select t1.id, substring_index(substring_index(t1.diagnosis_code_list, ',', ht.help_topic_id + 1), ',', -1) as diagnosis_code
                from (
                    select iwo2.id, concat_ws(',', iwo2.main_diagnosis_code , iwo2.other_diagnosis_code) as diagnosis_code_list
                    from insurance_work_order2 as iwo2
                    left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
                    where iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
                ) as t1
                join mysql.help_topic as ht on ht.help_topic_id <![CDATA[ < ]]> length(t1.diagnosis_code_list) - length(replace(t1.diagnosis_code_list, ',', '')) + 1
            ) as t2
            left join insurance_diagnosis as id on t2.diagnosis_code = id.icd
            where id.icd is not null
        ) as t3
        group by t3.diagnosis_code
        order by count(t3.id) desc
        limit 0, #{top}
    </select>

    <select id="selectList2" resultType="cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.WorkOrder2ExcelVO">
        select ic.name as company_name,
               iwo2.id,
               iwo2.type,
               iwo2.treatment_serial_number_type,
               iwo2.name,
               iwo2.id_card_number,
               iwo2.mobile_phone_number,
               iwo2.hospital_name,
               iwo2.treatment_datetime,
               iwo2.suggest_compensating_money,
               iwo2.actual_money,
               iwo2.complete_status,
               iwo2.create_time,
               iwo2.status,
               iwo2.area_id,
               iwo2.address,
               iwo2.electronic_bill_ids,
               case when ia.id is null then '未授权' else '已授权' end as auth_status,
               pr.hj_address
        from insurance_work_order2 as iwo2
        left join insurance_work_order_pdf as iwop on  iwop.order_id = iwo2.id
        left join insurance_area_company as iac on iwo2.area_id = iac.area_id
        LEFT JOIN insurance_company as ic on iac.company_id = ic.id
        left join insurance_work_order_event as iwoe on iwoe.work_order_id = iwo2.id and iwoe.`type` = 4
        left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
        left join insurance_permanent_resident pr on iwo2.id_card_number = pr.idcard
        <where>
            iac.agreement_date &lt;= iwo2.treatment_datetime and iac.agreement_end_date > iwo2.treatment_datetime and JSON_CONTAINS(iac.types, concat('[', iwo2.type, ']'))  and iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
            <if test="reqVO.hospitalCode != null and reqVO.hospitalCode != ''"> and iwo2.hospital_code = #{reqVO.hospitalCode}</if>
            <if test="reqVO.hospitalName != null and reqVO.hospitalName != ''"> and iwo2.hospital_name = #{reqVO.hospitalName}</if>
            <if test="reqVO.treatmentSerialNumberType != null"> and iwo2.treatment_serial_number_type = #{reqVO.treatmentSerialNumberType}</if>
            <if test="reqVO.treatmentSerialNumber != null and reqVO.treatmentSerialNumber != ''"> and iwo2.treatment_serial_number = #{reqVO.treatmentSerialNumber}</if>
            <if test="reqVO.name != null and reqVO.name != ''"> and iwo2.name = #{reqVO.name}</if>
            <if test="reqVO.idCardNumber != null and reqVO.idCardNumber != ''"> and iwo2.id_card_number = #{reqVO.idCardNumber}</if>
            <if test="reqVO.mobilePhoneNumber != null and reqVO.mobilePhoneNumber != ''"> and iwo2.mobile_phone_number = #{reqVO.mobilePhoneNumber}</if>
            <if test="reqVO.socialMedicareCardNumber != null and reqVO.socialMedicareCardNumber != ''"> and iwo2.social_medicare_card_number = #{reqVO.socialMedicareCardNumber}</if>
            <if test="reqVO.status != null"> and iwo2.status = #{reqVO.status}</if>
            <if test="reqVO.completeStatus != null and reqVO.completeStatus != ''"> and iwo2.complete_status = #{reqVO.completeStatus}</if>
            <if test="reqVO.type != null"> and iwo2.type = #{reqVO.type}</if>
            <if test="reqVO.types != null and reqVO.types.size() &gt; 0">
                and iwo2.type in
                <foreach collection="reqVO.types" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="not (reqVO.types != null and reqVO.types.size() &gt; 0)">
                and 1 = 2
            </if>
        </where>
        order by iwoe.create_time desc
    </select>

    <select id="selectDistinctHospitalNameList" resultType="java.lang.String">
        SELECT DISTINCT iwo2.hospital_name
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        WHERE iwo2.hospital_name IS NOT NULL AND iwo2.deleted = 0 AND (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="getWorkOrderStat"
            resultType="cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.WorkOrderStatRespVo">
        select c.id, c.name as company_name,
        SUM(case WHEN iwo2.`status` = 0 then 1 else 0 end ) as wait_taking_count,
        SUM(case WHEN iwo2.`status` = 2 then 1 else 0 end ) as wait_processing_count,
        SUM(case WHEN iwo2.`status` = 3 then 1 else 0 end ) as wait_visiting_count,
        SUM(case WHEN iwo2.`status` = 4 then 1 else 0 end ) as finished_count,
        SUM(case WHEN iwo2.`status` = 5 then 1 else 0 end ) as reject_count,
        SUM(case WHEN iwo2.`status` = 6 then 1 else 0 end ) as delay_count,
        SUM(iwo2.actual_money) as money,
        SUM(case WHEN iwo2.treatment_serial_number_type = 0 then iwo2.actual_money else 0 end) as outpatient_money,
        SUM(case WHEN iwo2.treatment_serial_number_type = 1 then iwo2.actual_money else 0 end) as inpatient_money
        from insurance_work_order2 iwo2
        left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
        left JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        left join insurance_company c on ac.company_id = c.id
        where iwo2.treatment_datetime >= ac.agreement_date and iwo2.treatment_datetime &lt; ac.agreement_end_date and JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']')) and iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
        <if test="reqVo.beginCreateTime != null"><![CDATA[ and iwo2.create_time >= #{reqVo.beginCreateTime}]]></if>
        <if test="reqVo.endCreateTime != null"><![CDATA[ and iwo2.create_time < #{reqVo.endCreateTime}]]></if>
        group by c.id, c.name
        having c.id != 5;
    </select>

    <select id="getHospitalDataStatistics" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.HospitalDataStatisticsVO">
        SELECT
            iwo2.hospital_name as hospitalName,
            SUM(CASE WHEN iwo2.create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as lastWeek,
            SUM(CASE WHEN iwo2.create_time >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) THEN 1 ELSE 0 END) as lastMonth,
            SUM(CASE WHEN iwo2.create_time >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR) THEN 1 ELSE 0 END) as lastYear,
            COUNT(*) as total
        FROM
            insurance_work_order2 as iwo2
        LEFT JOIN
            insurance_auth as ia on iwo2.id_card_number = ia.idcard
        WHERE
            iwo2.hospital_name IS NOT NULL and iwo2.hospital_name != '' and iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
        GROUP BY
            iwo2.hospital_name
        ORDER BY
            total DESC
    </select>

    <select id="getHospitalDetailStatistics" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.HospitalDetailStatisticsVO">
        SELECT
            #{hospitalName} as hospitalName,
            SUM(CASE WHEN iwo2.type = 1 AND iwo2.treatment_serial_number_type = 0 THEN 1 ELSE 0 END) as elderlyOutpatientCount,
            SUM(CASE WHEN iwo2.type = 1 AND iwo2.treatment_serial_number_type = 1 THEN 1 ELSE 0 END) as elderlyInpatientCount,
            SUM(CASE WHEN iwo2.type IN (7,8,9) AND iwo2.treatment_serial_number_type = 0 THEN 1 ELSE 0 END) as disabledOutpatientCount,
            SUM(CASE WHEN iwo2.type IN (7,8,9) AND iwo2.treatment_serial_number_type = 1 THEN 1 ELSE 0 END) as disabledInpatientCount
        FROM
            insurance_work_order2 as iwo2
        LEFT JOIN
            insurance_auth as ia on iwo2.id_card_number = ia.idcard
        WHERE
            iwo2.hospital_name = #{hospitalName} and iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="getInsuranceCompanyAnalysis" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.InsuranceCompanyAnalysisVO">
        SELECT
            ic.id as companyId,
            ic.name as companyName,
            SUM(CASE WHEN iwo2.status != 0 AND iwo2.create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as lastWeek,
            SUM(CASE WHEN iwo2.status != 0 AND iwo2.create_time >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) THEN 1 ELSE 0 END) as lastMonth,
            SUM(CASE WHEN iwo2.status != 0 AND iwo2.create_time >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR) THEN 1 ELSE 0 END) as lastYear,
            COUNT(iwo2.id) as total,
            IFNULL(SUM(CASE WHEN iwo2.status = 4 THEN iwo2.actual_money ELSE 0 END), 0) as compensatingMoney,
            SUM(CASE WHEN iwo2.status = 4 THEN 1 ELSE 0 END) as compensatingPersonNumber
        FROM
            insurance_company ic
        LEFT JOIN
            insurance_area_company iac ON ic.id = iac.company_id
        LEFT JOIN
            insurance_work_order2 iwo2 ON iwo2.area_id = iac.area_id
            AND iwo2.treatment_datetime >= iac.agreement_date
            AND iwo2.treatment_datetime &lt; iac.agreement_end_date
            AND JSON_CONTAINS(iac.types, CONCAT('[', iwo2.type, ']'))
            AND iwo2.deleted = 0
        LEFT JOIN
            insurance_auth as ia on iwo2.id_card_number = ia.idcard
        WHERE
            (ia.id is not null or iwo2.status = 4)
        GROUP BY
            ic.id, ic.name
        ORDER BY
            total DESC
    </select>

    <select id="getInsuranceCompanyDetail" resultType="cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.InsuranceCompanyDetailVO">
        SELECT
            ic.id as companyId,
            ic.name as companyName,
            SUM(CASE WHEN iwo2.status = 0 THEN 1 ELSE 0 END) as waitAcceptOrder,
            SUM(CASE WHEN iwo2.status = 2 THEN 1 ELSE 0 END) as waitHandle,
            SUM(CASE WHEN iwo2.status = 3 THEN 1 ELSE 0 END) as waitVisiting,
            SUM(CASE WHEN iwo2.status = 4 THEN 1 ELSE 0 END) as finished,
            SUM(CASE WHEN iwo2.status = 5 THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN iwo2.status = 6 THEN 1 ELSE 0 END) as delay,
            COUNT(iwo2.id) as total,
            IFNULL(SUM(CASE WHEN iwo2.status = 4 THEN iwo2.actual_money ELSE 0 END), 0) as compensatingMoney
        FROM
            insurance_company ic
        LEFT JOIN
            insurance_area_company iac ON ic.id = iac.company_id
        LEFT JOIN
            insurance_work_order2 iwo2 ON iwo2.area_id = iac.area_id
            AND iwo2.treatment_datetime >= iac.agreement_date
            AND iwo2.treatment_datetime &lt; iac.agreement_end_date
            AND JSON_CONTAINS(iac.types, CONCAT('[', iwo2.type, ']'))
            AND iwo2.deleted = 0
        LEFT JOIN
            insurance_auth as ia on iwo2.id_card_number = ia.idcard
        WHERE
            ic.id = #{companyId} and (ia.id is not null or iwo2.status = 4)
        GROUP BY
            ic.id, ic.name
    </select>

    <select id="countWorkOrderPdf" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order_pdf
    </select>

    <select id="countSignedWorkOrderPdf" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order_pdf
        WHERE signed_pdf IS NOT NULL AND signed_pdf != ''
    </select>

    <select id="countWorkOrderByTimeRange" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        WHERE iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)
        <if test="beginTime != null">
            AND iwo2.create_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            AND iwo2.create_time &lt;= #{endTime}
        </if>
    </select>

    <!-- Custom count methods with authorization table joins -->
    <select id="countWithAuth" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        LEFT JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        LEFT JOIN insurance_company c on ac.company_id = c.id
        WHERE iwo2.treatment_datetime >= ac.agreement_date
        AND iwo2.treatment_datetime &lt; ac.agreement_end_date
        AND JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']'))
        AND iwo2.deleted = 0 AND (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countWaitAcceptOrderWithAuth" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        LEFT JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        LEFT JOIN insurance_company c on ac.company_id = c.id
        WHERE iwo2.treatment_datetime >= ac.agreement_date
        AND iwo2.treatment_datetime &lt; ac.agreement_end_date
        AND JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']'))
        AND iwo2.deleted = 0 AND iwo2.status = 0 AND (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countWaitHandleWithAuth" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        LEFT JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        LEFT JOIN insurance_company c on ac.company_id = c.id
        WHERE iwo2.treatment_datetime >= ac.agreement_date
        AND iwo2.treatment_datetime &lt; ac.agreement_end_date
        AND JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']'))
        AND iwo2.deleted = 0 AND iwo2.status = 2 AND (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countWaitVisitingWithAuth" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        LEFT JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        LEFT JOIN insurance_company c on ac.company_id = c.id
        WHERE iwo2.treatment_datetime >= ac.agreement_date
        AND iwo2.treatment_datetime &lt; ac.agreement_end_date
        AND JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']'))
        AND iwo2.deleted = 0 AND iwo2.status = 3 AND (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countFinishedWithAuth" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        LEFT JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        LEFT JOIN insurance_company c on ac.company_id = c.id
        WHERE iwo2.treatment_datetime >= ac.agreement_date
        AND iwo2.treatment_datetime &lt; ac.agreement_end_date
        AND JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']'))
        AND iwo2.deleted = 0 AND iwo2.status = 4 AND (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countRejectedWithAuth" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        LEFT JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        LEFT JOIN insurance_company c on ac.company_id = c.id
        WHERE iwo2.treatment_datetime >= ac.agreement_date
        AND iwo2.treatment_datetime &lt; ac.agreement_end_date
        AND JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']'))
        AND iwo2.deleted = 0 AND iwo2.status = 5 AND (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countDelayWithAuth" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        LEFT JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        LEFT JOIN insurance_company c on ac.company_id = c.id
        WHERE iwo2.treatment_datetime >= ac.agreement_date
        AND iwo2.treatment_datetime &lt; ac.agreement_end_date
        AND JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']'))
        AND iwo2.deleted = 0 AND iwo2.status = 6 AND (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countSumAcceptOrderWithAuth" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        LEFT JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        LEFT JOIN insurance_company c on ac.company_id = c.id
        WHERE iwo2.treatment_datetime >= ac.agreement_date
        AND iwo2.treatment_datetime &lt; ac.agreement_end_date
        AND JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']'))
        AND iwo2.deleted = 0 AND iwo2.status IN (1, 2, 3, 4) AND (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countNoDataOrderWithAuth" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        LEFT JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        LEFT JOIN insurance_company c on ac.company_id = c.id
        WHERE iwo2.treatment_datetime >= ac.agreement_date
        AND iwo2.treatment_datetime &lt; ac.agreement_end_date
        AND JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']'))
        AND iwo2.deleted = 0 AND iwo2.complete_status = 'NO_DATA' AND (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countUnsureOrderWithAuth" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        LEFT JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        LEFT JOIN insurance_company c on ac.company_id = c.id
        WHERE iwo2.treatment_datetime >= ac.agreement_date
        AND iwo2.treatment_datetime &lt; ac.agreement_end_date
        AND JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']'))
        AND iwo2.deleted = 0 AND iwo2.complete_status = 'UNSURE' AND (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countCompleteOrderWithAuth" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        LEFT JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        LEFT JOIN insurance_company c on ac.company_id = c.id
        WHERE iwo2.treatment_datetime >= ac.agreement_date
        AND iwo2.treatment_datetime &lt; ac.agreement_end_date
        AND JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']'))
        AND iwo2.deleted = 0 AND iwo2.complete_status = 'COMPLETE' AND (ia.id is not null or iwo2.status = 4)
    </select>

    <select id="countWorkOrderWithAuth" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        LEFT JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        LEFT JOIN insurance_company c on ac.company_id = c.id
        WHERE iwo2.treatment_datetime >= ac.agreement_date
        AND iwo2.treatment_datetime &lt; ac.agreement_end_date
        AND JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']'))
        AND iwo2.deleted = 0 AND (ia.id is not null or iwo2.status = 4)
        AND iwo2.type IN
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        AND iwo2.status = #{status}
    </select>

    <select id="selectDistinctHospitalNameListWithAuth" resultType="java.lang.String">
        SELECT DISTINCT iwo2.hospital_name
        FROM insurance_work_order2 as iwo2
        LEFT JOIN insurance_auth as ia on iwo2.id_card_number = ia.idcard
        LEFT JOIN insurance_area a on a.id = iwo2.area_id
        LEFT JOIN insurance_area_company ac on a.id = ac.area_id
        LEFT JOIN insurance_company c on ac.company_id = c.id
        WHERE iwo2.treatment_datetime >= ac.agreement_date
        AND iwo2.treatment_datetime &lt; ac.agreement_end_date
        AND JSON_CONTAINS(ac.types, concat('[', iwo2.type, ']'))
        AND iwo2.hospital_name IS NOT NULL AND iwo2.deleted = 0 AND (ia.id is not null or iwo2.status = 4)
    </select>

    <!-- 批量更新工单状态为拒绝状态 -->
    <update id="batchUpdateWorkOrdersToReject">
        UPDATE insurance_work_order2
        SET
            original_status = status,
            status = 7,
            batch_operation_id = #{batchId},
            update_time = NOW()
        WHERE id IN
        <foreach collection="workOrderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 批量恢复工单状态 -->
    <update id="batchRecoverWorkOrders">
        UPDATE insurance_work_order2
        SET
            status = original_status,
            original_status = NULL,
            batch_operation_id = NULL,
            update_time = NOW()
        WHERE id IN
        <foreach collection="workOrderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
        AND original_status IS NOT NULL
    </update>
</mapper>
