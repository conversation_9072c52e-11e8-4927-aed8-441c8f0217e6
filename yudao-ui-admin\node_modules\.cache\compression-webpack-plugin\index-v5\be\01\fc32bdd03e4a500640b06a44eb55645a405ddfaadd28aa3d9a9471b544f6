
2620fe5038a31af34ff7c1edec32a0b64364ad70	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"1c4c7cd19b80d325ad4b72b1863eb8e3\"}","integrity":"sha512-/BJiFI+hfAy9fnQTisYitaQOgujbvHjpmrUrIMcxHKNru3MNtbUu1zs283WKYmmEWT5uZCAzKpaYkrbz3zLE0w==","time":1754286885923,"size":3474563}