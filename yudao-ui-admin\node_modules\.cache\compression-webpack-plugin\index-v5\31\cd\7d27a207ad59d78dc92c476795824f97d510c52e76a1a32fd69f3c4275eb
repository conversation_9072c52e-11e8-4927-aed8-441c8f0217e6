
78d31f058bfd5f5bb6d1b86a55bb81aa74fcf2bc	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.b8d0ce630de31433683c.hot-update.js\",\"contentHash\":\"897e9287bab4a54905e12a7a81a49aa1\"}","integrity":"sha512-jvG4AJZv807j0JDEJSpUtvr8C+yaiRjTLBsyL8AUAda2JlKg/o5ElCSXOIz7haV7b31YJGFMnRES2W5/J4oqvQ==","time":1754286851323,"size":169865}