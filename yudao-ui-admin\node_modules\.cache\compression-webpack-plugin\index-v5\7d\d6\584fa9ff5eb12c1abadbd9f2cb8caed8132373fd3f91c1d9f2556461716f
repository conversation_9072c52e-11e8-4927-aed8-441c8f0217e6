
4aa797ed5ca61625e3ec4ba91fb5ef450d9d7a78	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"25f831dda1bd702f19400646347db62a\"}","integrity":"sha512-WoqSc8xl3S67GW/Ontengt355+ArP/q+cJLCGNA4ieBuwSaCEKueija5a+BVg+ePMODOAdglPU8Vbvi2zePU2g==","time":1754286911078,"size":16761192}