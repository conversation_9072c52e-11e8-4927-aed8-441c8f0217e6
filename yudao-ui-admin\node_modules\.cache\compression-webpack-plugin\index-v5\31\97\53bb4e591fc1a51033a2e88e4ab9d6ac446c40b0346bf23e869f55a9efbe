
e72346ee526c2e7a438448227b17594b34a2cc7c	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"8872904d101ca6fe1c246094bb4f2a05\"}","integrity":"sha512-cvLoxcVDh6FuG0JWhF4GmKj3v4pu1TiDTt25aMH7A+heKOaLlcOErNnlx5l7ujCWIegshXwp43uP5EWGLkE17g==","time":1754286851690,"size":3473692}