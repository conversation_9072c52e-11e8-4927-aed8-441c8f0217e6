
e62bbbab94d38b591a327f7e8d0d6c6a17d748b8	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"44d50c76626e394cfada689c6d7c4ff1\"}","integrity":"sha512-uPqE5Hfly33mcLpsHKY9ZBZ9oQFoQYeLstaKYGboU0itOZGBa4jPHhzAZuOPdbHbWjTnIguJCg7/cWoRorxR4w==","time":1754286834170,"size":16741935}