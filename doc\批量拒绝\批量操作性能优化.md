# 批量操作性能优化

## 优化背景

在批量拒绝和批量恢复工单的操作中，原始实现存在性能问题：

1. **查询性能问题**：使用 `selectList()` 查询所有字段，包括大字段如电子票据信息
2. **更新性能问题**：使用 `updateById()` 逐条更新，效率低下
3. **内存占用问题**：加载完整的工单对象到内存，占用大量内存

## 优化方案

### 1. 查询优化

**优化前**：
```java
// 查询所有字段
List<WorkOrder2DO> workOrdersToReject = workOrder2Mapper.selectList(
    new LambdaQueryWrapperX<WorkOrder2DO>()
        .eq(WorkOrder2DO::getStatus, WorkOrderStatusEnum.WAIT_TAKING.getStatus())
        .le(WorkOrder2DO::getTreatmentDatetime, cutoffDate)
        .eq(WorkOrder2DO::getDeleted, false)
);
```

**优化后**：
```java
// 只查询必要字段
List<WorkOrder2DO> workOrdersToReject = workOrder2Mapper.selectWorkOrdersForBatchReject(cutoffDate);
```

**新增Mapper方法**：
```java
default List<WorkOrder2DO> selectWorkOrdersForBatchReject(Date cutoffDate) {
    return selectList(
        new LambdaQueryWrapperX<WorkOrder2DO>()
            .select(WorkOrder2DO::getId, WorkOrder2DO::getStatus)
            .eq(WorkOrder2DO::getStatus, WorkOrderStatusEnum.WAIT_TAKING.getStatus())
            .le(WorkOrder2DO::getTreatmentDatetime, cutoffDate)
            .eq(WorkOrder2DO::getDeleted, false)
    );
}
```

### 2. 更新优化

**优化前**：
```java
// 逐条更新
for (WorkOrder2DO workOrder : workOrdersToReject) {
    workOrder.setOriginalStatus(workOrder.getStatus());
    workOrder.setBatchOperationId(batchId);
    workOrder.setStatus(WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus());
    workOrder2Mapper.updateById(workOrder);
}
```

**优化后**：
```java
// 批量更新
if (processedCount > 0) {
    List<Long> workOrderIds = workOrdersToReject.stream()
        .map(WorkOrder2DO::getId)
        .collect(Collectors.toList());
    workOrder2Mapper.batchUpdateWorkOrdersToReject(workOrderIds, batchId);
}
```

**新增批量更新SQL**：
```xml
<update id="batchUpdateWorkOrdersToReject">
    UPDATE insurance_work_order2 
    SET 
        original_status = status,
        status = 7,
        batch_operation_id = #{batchId},
        update_time = NOW()
    WHERE id IN
    <foreach collection="workOrderIds" item="id" open="(" separator="," close=")">
        #{id}
    </foreach>
    AND deleted = 0
</update>
```

## 实现细节

### 1. 新增Mapper方法

在 `WorkOrder2Mapper.java` 中添加：

```java
/**
 * 查询符合批量拒绝条件的工单（只查询必要字段）
 */
default List<WorkOrder2DO> selectWorkOrdersForBatchReject(Date cutoffDate);

/**
 * 查询符合批量恢复条件的工单（只查询必要字段）
 */
default List<WorkOrder2DO> selectWorkOrdersForBatchRecover(String batchOperationId);

/**
 * 批量更新工单状态为拒绝状态
 */
int batchUpdateWorkOrdersToReject(@Param("workOrderIds") List<Long> workOrderIds, @Param("batchId") String batchId);

/**
 * 批量恢复工单状态
 */
int batchRecoverWorkOrders(@Param("workOrderIds") List<Long> workOrderIds);
```

### 2. 新增XML映射

在 `WorkOrder2Mapper.xml` 中添加：

```xml
<!-- 批量更新工单状态为拒绝状态 -->
<update id="batchUpdateWorkOrdersToReject">
    UPDATE insurance_work_order2 
    SET 
        original_status = status,
        status = 7,
        batch_operation_id = #{batchId},
        update_time = NOW()
    WHERE id IN
    <foreach collection="workOrderIds" item="id" open="(" separator="," close=")">
        #{id}
    </foreach>
    AND deleted = 0
</update>

<!-- 批量恢复工单状态 -->
<update id="batchRecoverWorkOrders">
    UPDATE insurance_work_order2 
    SET 
        status = original_status,
        original_status = NULL,
        batch_operation_id = NULL,
        update_time = NOW()
    WHERE id IN
    <foreach collection="workOrderIds" item="id" open="(" separator="," close=")">
        #{id}
    </foreach>
    AND deleted = 0
    AND original_status IS NOT NULL
</update>
```

### 3. 修改Service实现

#### 批量拒绝优化

```java
// 查询符合条件的工单（只查询必要字段）
List<WorkOrder2DO> workOrdersToReject = workOrder2Mapper.selectWorkOrdersForBatchReject(cutoffDate);

int processedCount = workOrdersToReject.size();

if (processedCount > 0) {
    // 提取工单ID列表
    List<Long> workOrderIds = workOrdersToReject.stream()
        .map(WorkOrder2DO::getId)
        .collect(Collectors.toList());

    // 批量更新工单状态（只更新必要字段）
    workOrder2Mapper.batchUpdateWorkOrdersToReject(workOrderIds, batchId);

    // 批量创建工单事件记录
    for (Long workOrderId : workOrderIds) {
        WorkOrderEventDo workOrderEvent = new WorkOrderEventDo();
        workOrderEvent.setWorkOrderId(workOrderId);
        workOrderEvent.setType(WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus());
        workOrderEventMapper.insert(workOrderEvent);
    }
}
```

#### 批量恢复优化

```java
// 查询需要恢复的工单（只查询必要字段）
List<WorkOrder2DO> workOrdersToRecover = workOrder2Mapper.selectWorkOrdersForBatchRecover(batchOperationId);

// 过滤出有原始状态的工单
List<WorkOrder2DO> validWorkOrders = workOrdersToRecover.stream()
    .filter(workOrder -> workOrder.getOriginalStatus() != null)
    .collect(Collectors.toList());

int processedCount = validWorkOrders.size();

if (processedCount > 0) {
    // 提取工单ID列表
    List<Long> workOrderIds = validWorkOrders.stream()
        .map(WorkOrder2DO::getId)
        .collect(Collectors.toList());

    // 批量恢复工单状态（只更新必要字段）
    workOrder2Mapper.batchRecoverWorkOrders(workOrderIds);

    // 批量创建工单事件记录
    for (WorkOrder2DO workOrder : validWorkOrders) {
        WorkOrderEventDo workOrderEvent = new WorkOrderEventDo();
        workOrderEvent.setWorkOrderId(workOrder.getId());
        workOrderEvent.setType(workOrder.getOriginalStatus()); // 恢复后的状态
        workOrderEventMapper.insert(workOrderEvent);
    }
}
```

## 性能提升

### 1. 查询性能提升

- **减少数据传输**：只查询必要字段（id, status, original_status），避免查询大字段
- **减少内存占用**：工单对象只包含必要字段，大幅减少内存使用
- **提高查询速度**：减少数据库I/O和网络传输时间

### 2. 更新性能提升

- **批量更新**：使用单条SQL语句更新多条记录，减少数据库交互次数
- **减少锁竞争**：批量操作减少数据库锁的获取和释放次数
- **提高事务效率**：减少事务执行时间

### 3. 预期性能改进

- **查询性能**：预计提升 60-80%（取决于大字段的大小）
- **更新性能**：预计提升 80-90%（批量更新 vs 逐条更新）
- **内存使用**：预计减少 70-90%（只加载必要字段）
- **整体执行时间**：预计减少 50-70%

## 注意事项

1. **字段选择**：确保查询的字段包含所有业务逻辑需要的字段
2. **批量大小**：对于超大批量操作，可能需要分批处理避免SQL语句过长
3. **事务管理**：保持原有的事务边界，确保数据一致性
4. **错误处理**：保持原有的错误处理逻辑
5. **测试验证**：充分测试优化后的功能，确保业务逻辑正确

## 文件修改清单

1. **WorkOrder2Mapper.java** - 添加新的查询和更新方法
2. **WorkOrder2Mapper.xml** - 添加批量更新的SQL语句
3. **WorkOrder2ServiceImpl.java** - 修改批量操作的实现逻辑

## 测试建议

1. **功能测试**：确保批量操作功能正常
2. **性能测试**：对比优化前后的执行时间和资源使用
3. **压力测试**：测试大批量数据的处理能力
4. **回归测试**：确保其他功能不受影响
