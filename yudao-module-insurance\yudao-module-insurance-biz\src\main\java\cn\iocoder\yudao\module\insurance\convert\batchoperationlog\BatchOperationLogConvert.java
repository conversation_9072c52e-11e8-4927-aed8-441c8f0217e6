package cn.iocoder.yudao.module.insurance.convert.batchoperationlog;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.BatchOperationLogRespVO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.batchoperationlog.BatchOperationLogDO;
import cn.iocoder.yudao.module.insurance.enums.batchoperation.BatchOperationStatusEnum;
import cn.iocoder.yudao.module.insurance.enums.batchoperation.BatchOperationTypeEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 批量操作日志 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BatchOperationLogConvert {

    BatchOperationLogConvert INSTANCE = Mappers.getMapper(BatchOperationLogConvert.class);

    @Mapping(target = "operationTypeDisplay", source = "operationType", qualifiedByName = "mapOperationType")
    @Mapping(target = "statusDisplay", source = "status", qualifiedByName = "mapStatus")
    @Mapping(target = "duration", expression = "java(calculateDuration(bean.getStartTime(), bean.getEndTime()))")
    BatchOperationLogRespVO convert(BatchOperationLogDO bean);

    List<BatchOperationLogRespVO> convertList(List<BatchOperationLogDO> list);

    PageResult<BatchOperationLogRespVO> convertPage(PageResult<BatchOperationLogDO> page);

    /**
     * 获取操作类型显示名称
     */
    @Named("mapOperationType")
    default String getOperationTypeDisplay(String operationType) {
        if (operationType == null) {
            return null;
        }
        try {
            BatchOperationTypeEnum typeEnum = BatchOperationTypeEnum.valueOf(operationType);
            return typeEnum.getName();
        } catch (IllegalArgumentException e) {
            return operationType;
        }
    }

    /**
     * 获取状态显示名称
     */
    @Named("mapStatus")
    default String getStatusDisplay(String status) {
        if (status == null) {
            return null;
        }
        try {
            BatchOperationStatusEnum statusEnum = BatchOperationStatusEnum.valueOf(status);
            return statusEnum.getName();
        } catch (IllegalArgumentException e) {
            return status;
        }
    }

    /**
     * 计算执行时长
     */
    default Long calculateDuration(java.util.Date startTime, java.util.Date endTime) {
        if (startTime == null || endTime == null) {
            return null;
        }
        return endTime.getTime() - startTime.getTime();
    }
}
