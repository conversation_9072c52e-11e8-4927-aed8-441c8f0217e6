import request from '@/utils/request'

// 创建工单
export function createWorkOrder(data) {
  return request({
    url: '/insurance/work-order/create',
    method: 'post',
    data: data
  })
}

// 更新工单
export function updateWorkOrder(data) {
  return request({
    url: '/insurance/work-order/update',
    method: 'put',
    data: data
  })
}

// 删除工单
export function deleteWorkOrder(id) {
  return request({
    url: '/insurance/work-order/delete?id=' + id,
    method: 'delete'
  })
}

// 获得工单
export function getWorkOrder(id) {
  return request({
    url: '/insurance/work-order/get?id=' + id,
    method: 'get'
  })
}

// 获得工单分页
export function getWorkOrderPage(query) {
  return request({
    url: '/insurance/work-order/page2',
    method: 'get',
    params: query
  })
}

// 导出工单 Excel
export function exportWorkOrderExcel(query) {
  return request({
    url: '/insurance/work-order/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 接单
export function takeWorkOrder(id) {
  return request({
    url: '/insurance/work-order/take?id=' + id,
    method: 'put'
  })
}

// 盖章
export function hospitalCheck(id) {
  return request({
    url: '/insurance/work-order/hospital-check?id=' + id,
    method: 'put'
  })
}

// 处理
export function process(id) {
  return request({
    url: '/insurance/work-order/process?id=' + id,
    method: 'put'
  })
}

// 回访
export function visit(data) {
  return request({
    url: '/insurance/work-order/visit',
    method: 'put',
    data: data
  })
}

// 拒绝
export function reject(data) {
  return request({
    url: '/insurance/work-order/reject',
    method: 'put',
    data: data
  })
}

// 延后
export function delay(data) {
  return request({
    url: '/insurance/work-order/delay',
    method: 'put',
    data: data
  })
}

// 回退
export function returnWorkOrder(id) {
  return request({
    url: '/insurance/work-order/return',
    method: 'put',
    data: {id}
  })
}

export function getHospitalNames() {
  return request({
    url: '/insurance/work-order/hospitalNames',
    method: 'get',
  })
}

//创建未签章pdf
export function createPdf(id) {
  return request({
    url: '/insurance/work-order/createPdf?id=' + id,
    method: 'put'
  })
}

//获取保险公司理赔统计数据
export function getWorkOrderStat(query) {
  return request({
    url: '/insurance/work-order/stat',
    method: 'get',
    params: query
  })
}

// 批量拒绝工单
export function batchRejectWorkOrders(data) {
  return request({
    url: '/insurance/work-order/batch/reject',
    method: 'post',
    data: data
  })
}

// 获得批量操作日志分页
export function getBatchOperationLogPage(query) {
  return request({
    url: '/insurance/work-order/batch-operation-logs/page',
    method: 'get',
    params: query
  })
}
