
5ff8337769c054412fcc20e45817a444c14d229d	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"6c0c6f1f7ef8524edda8258613208049\"}","integrity":"sha512-57GlDWIBUYWF7B4MffCoD9h+DwNofesPl6WYpf/h2roskLXRzk4C59s5gJn00XAOMy+JQx+nmL5eaw7nMyxBeQ==","time":1754286852584,"size":16754682}