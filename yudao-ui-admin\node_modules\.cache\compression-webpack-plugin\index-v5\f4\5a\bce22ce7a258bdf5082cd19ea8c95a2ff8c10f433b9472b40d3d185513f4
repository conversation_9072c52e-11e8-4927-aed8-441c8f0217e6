
d5aeca94b231a1ebb7b93f40aab5d8db487adaa3	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"73330b046aa425bea06ade0c4c01fe61\"}","integrity":"sha512-cZhv3/nGyPH1l3cclYJ2xPp/J0h3RSRkzbMT0F7txjQt7vm6C+grTq3jHgO3ZYt+7NxAoMGpkLcErOvBdOTqQw==","time":1754286814809,"size":3474870}