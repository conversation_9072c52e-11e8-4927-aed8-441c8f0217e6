
c400eaf2e270b34e4b47cdd6c79e484fb5ce4e28	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"12123d3a0d2364633e0eca7ee79adb29\"}","integrity":"sha512-vVaxcWEc5xBsy3OpS5JFq0EvcR0ybk2tqHiioh+rYnBpmpSfqd8wZ17oGLemuGcZuvvtxawbRUi4fcuN9u86fQ==","time":1754286910235,"size":3473651}