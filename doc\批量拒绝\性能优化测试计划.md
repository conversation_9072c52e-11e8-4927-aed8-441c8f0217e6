# 批量操作性能优化测试计划

## 测试目标

验证批量拒绝和批量恢复操作的性能优化效果，确保功能正确性和性能提升。

## 测试环境

### 数据准备
1. 准备不同规模的测试数据：
   - 小批量：100条工单
   - 中批量：1,000条工单  
   - 大批量：10,000条工单
   - 超大批量：50,000条工单

2. 工单数据特征：
   - 状态为"待接单"（status=0）
   - 包含电子票据等大字段数据
   - 分布在不同的就医时间

### 测试工具
- 数据库性能监控工具
- JVM内存监控
- 应用性能监控（APM）
- 自定义性能测试脚本

## 测试用例

### 1. 功能正确性测试

#### 测试用例1.1：批量拒绝功能验证
**目标**：验证优化后的批量拒绝功能正确性

**步骤**：
1. 准备100条待接单工单
2. 执行批量拒绝操作
3. 验证结果

**预期结果**：
- 工单状态正确更新为"行政拒绝"（status=7）
- original_status字段正确保存原始状态
- batch_operation_id字段正确设置
- 工单事件记录正确创建
- 批量操作日志正确记录

#### 测试用例1.2：批量恢复功能验证
**目标**：验证优化后的批量恢复功能正确性

**步骤**：
1. 使用测试用例1.1的结果数据
2. 执行批量恢复操作
3. 验证结果

**预期结果**：
- 工单状态正确恢复到原始状态
- original_status字段正确清空
- batch_operation_id字段正确清空
- 工单事件记录正确创建
- 批量操作日志正确记录

### 2. 性能对比测试

#### 测试用例2.1：查询性能对比
**目标**：对比优化前后的查询性能

**测试方法**：
```sql
-- 优化前：查询所有字段
SELECT * FROM insurance_work_order2 
WHERE status = 0 AND treatment_datetime <= '2024-04-30 23:59:59' AND deleted = 0;

-- 优化后：只查询必要字段
SELECT id, status FROM insurance_work_order2 
WHERE status = 0 AND treatment_datetime <= '2024-04-30 23:59:59' AND deleted = 0;
```

**测量指标**：
- 查询执行时间
- 数据传输量
- 内存使用量

#### 测试用例2.2：更新性能对比
**目标**：对比优化前后的更新性能

**测试方法**：
```java
// 优化前：逐条更新
for (WorkOrder2DO workOrder : workOrders) {
    // 设置字段
    workOrder2Mapper.updateById(workOrder);
}

// 优化后：批量更新
workOrder2Mapper.batchUpdateWorkOrdersToReject(workOrderIds, batchId);
```

**测量指标**：
- 更新执行时间
- 数据库连接数
- 事务执行时间

### 3. 压力测试

#### 测试用例3.1：大批量数据处理
**目标**：测试大批量数据的处理能力

**测试数据**：10,000条工单

**测试步骤**：
1. 执行批量拒绝操作
2. 监控系统资源使用
3. 记录执行时间
4. 验证数据正确性

**性能指标**：
- 总执行时间 < 30秒
- 内存使用峰值 < 500MB
- CPU使用率 < 80%
- 数据库连接数 < 10

#### 测试用例3.2：并发操作测试
**目标**：测试并发批量操作的稳定性

**测试方法**：
1. 同时启动5个批量拒绝操作
2. 监控系统稳定性
3. 验证数据一致性

**预期结果**：
- 所有操作成功完成
- 无数据冲突
- 系统保持稳定

### 4. 内存使用测试

#### 测试用例4.1：内存占用对比
**目标**：对比优化前后的内存使用

**测试方法**：
1. 分别执行优化前后的批量操作
2. 监控JVM内存使用
3. 记录峰值内存占用

**预期结果**：
- 优化后内存使用减少70%以上
- 无内存泄漏
- GC频率降低

## 性能基准

### 查询性能基准
| 数据量 | 优化前时间 | 优化后时间 | 提升比例 |
|--------|------------|------------|----------|
| 100条  | 50ms       | 20ms       | 60%      |
| 1,000条| 500ms      | 150ms      | 70%      |
| 10,000条| 5s        | 1.5s       | 70%      |

### 更新性能基准
| 数据量 | 优化前时间 | 优化后时间 | 提升比例 |
|--------|------------|------------|----------|
| 100条  | 200ms      | 30ms       | 85%      |
| 1,000条| 2s         | 200ms      | 90%      |
| 10,000条| 20s       | 1.5s       | 92.5%    |

### 内存使用基准
| 数据量 | 优化前内存 | 优化后内存 | 减少比例 |
|--------|------------|------------|----------|
| 100条  | 50MB       | 15MB       | 70%      |
| 1,000条| 500MB      | 100MB      | 80%      |
| 10,000条| 5GB       | 800MB      | 84%      |

## 测试脚本示例

### 性能测试脚本
```java
@Test
public void testBatchRejectPerformance() {
    // 准备测试数据
    List<WorkOrder2DO> testData = prepareTestData(1000);
    
    // 记录开始时间
    long startTime = System.currentTimeMillis();
    
    // 执行批量拒绝
    BatchRejectRespVO result = workOrder2Service.batchRejectWorkOrders(cutoffDate);
    
    // 记录结束时间
    long endTime = System.currentTimeMillis();
    
    // 验证结果
    assertEquals(1000, result.getProcessedCount());
    
    // 验证性能
    long executionTime = endTime - startTime;
    assertTrue("执行时间应小于2秒", executionTime < 2000);
    
    // 验证内存使用
    Runtime runtime = Runtime.getRuntime();
    long memoryUsed = runtime.totalMemory() - runtime.freeMemory();
    assertTrue("内存使用应小于200MB", memoryUsed < 200 * 1024 * 1024);
}
```

### 数据验证脚本
```java
@Test
public void testBatchRejectCorrectness() {
    // 准备测试数据
    List<WorkOrder2DO> testData = prepareTestData(100);
    List<Long> originalIds = testData.stream()
        .map(WorkOrder2DO::getId)
        .collect(Collectors.toList());
    
    // 执行批量拒绝
    BatchRejectRespVO result = workOrder2Service.batchRejectWorkOrders(cutoffDate);
    
    // 验证工单状态
    List<WorkOrder2DO> updatedWorkOrders = workOrder2Mapper.selectBatchByIds(originalIds);
    for (WorkOrder2DO workOrder : updatedWorkOrders) {
        assertEquals("状态应为行政拒绝", 
            WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus(), 
            workOrder.getStatus());
        assertEquals("原始状态应为待接单", 
            WorkOrderStatusEnum.WAIT_TAKING.getStatus(), 
            workOrder.getOriginalStatus());
        assertNotNull("批次号不应为空", workOrder.getBatchOperationId());
    }
    
    // 验证事件记录
    for (Long workOrderId : originalIds) {
        List<WorkOrderEventDo> events = workOrderEventMapper.selectByWorkOrderId(workOrderId);
        assertTrue("应有事件记录", events.size() > 0);
        assertEquals("最新事件类型应为行政拒绝", 
            WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus(),
            events.get(events.size() - 1).getType());
    }
}
```

## 测试报告模板

### 性能测试报告
- **测试日期**：
- **测试环境**：
- **测试数据量**：
- **优化前性能**：
- **优化后性能**：
- **性能提升比例**：
- **内存使用对比**：
- **功能正确性验证**：
- **问题和建议**：

## 验收标准

1. **功能正确性**：所有功能测试用例通过
2. **性能提升**：
   - 查询性能提升 ≥ 60%
   - 更新性能提升 ≥ 80%
   - 内存使用减少 ≥ 70%
3. **稳定性**：压力测试和并发测试通过
4. **兼容性**：不影响现有功能
