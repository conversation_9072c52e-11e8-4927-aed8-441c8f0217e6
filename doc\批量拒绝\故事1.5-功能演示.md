# 故事1.5 功能演示指南

## 功能概述

批量拒绝旧工单功能允许系统管理员通过简单的用户界面批量拒绝指定日期及之前的所有待接单工单，无需直接操作数据库。

## 界面位置

功能入口位于工单管理页面的顶部操作区：
- 路径：工单管理 → 顶部操作栏
- 按钮名称：**批量拒绝旧工单**
- 按钮位置：在"批量操作日志"按钮旁边

## 操作流程

### 第一步：点击批量拒绝按钮
1. 进入工单管理页面
2. 在顶部操作栏找到红色的"批量拒绝旧工单"按钮
3. 点击按钮（需要相应权限）

### 第二步：设置截止日期
1. 弹出"批量拒绝历史待接单工单"模态框
2. 阅读操作说明文字
3. 在"截止日期"字段选择一个日期
4. 系统将拒绝该日期及之前的所有待接单工单

### 第三步：执行操作
1. 点击红色的"执行"按钮
2. 系统弹出二次确认对话框
3. 确认对话框显示：`您确定要拒绝 YYYY-MM-DD 及之前的所有待接单工单吗？此操作可通过日志恢复。`
4. 点击"确定执行"按钮

### 第四步：查看结果
1. 系统执行批量拒绝操作
2. 执行期间按钮显示加载状态
3. 操作完成后显示成功提示：`操作成功！批次号：[batchId]，共处理了 [processedCount] 条工单。`
4. 工单列表自动刷新

## 权限要求

使用此功能需要以下权限：
- `insurance:work-order:batch-reject`

没有权限的用户将看不到"批量拒绝旧工单"按钮。

## 安全机制

### 二次确认
- 点击"执行"后必须通过二次确认对话框
- 确认信息包含具体的截止日期
- 防止误操作

### 操作审计
- 每次批量操作都会生成唯一的批次号
- 完整的操作日志记录在批量操作日志中
- 可通过批量操作日志查看和恢复

### 权限控制
- 只有授权用户才能执行批量操作
- 与现有权限系统集成

## 错误处理

### 表单验证
- 截止日期为必填项
- 未选择日期时无法执行操作

### API错误
- 网络错误或服务器错误时保持模态框打开
- 显示具体的错误信息
- 允许用户重试操作

### 业务错误
- 如果没有符合条件的工单，系统会正常返回处理数量为0
- 操作日志中会记录具体的执行结果

## 恢复机制

如果需要恢复被批量拒绝的工单：

1. 进入"批量操作日志"
2. 找到对应的批量拒绝记录
3. 记录批次号
4. 使用批量恢复功能（故事1.3实现）

## 注意事项

### 操作影响
- 只影响状态为"待接单"（status=0）的工单
- 只影响就医时间小于等于截止日期的工单
- 被拒绝的工单状态变为"行政拒绝"（status=7）

### 最佳实践
- 建议在非业务高峰期执行批量操作
- 执行前确认截止日期的准确性
- 执行后检查批量操作日志确认结果
- 必要时准备恢复方案

### 性能考虑
- 大批量操作可能需要较长时间
- 操作期间请勿关闭浏览器或刷新页面
- 系统会显示加载状态直到操作完成

## 故障排除

### 按钮不显示
- 检查用户是否有 `insurance:work-order:batch-reject` 权限
- 确认页面已正确加载

### 操作失败
- 检查网络连接
- 确认后端服务正常运行
- 查看浏览器控制台错误信息
- 联系技术支持

### 结果异常
- 检查批量操作日志中的详细信息
- 确认筛选条件是否正确
- 验证工单数据状态

## 相关功能

- **批量操作日志**：查看所有批量操作的历史记录
- **批量恢复**：恢复被批量拒绝的工单
- **工单详情**：查看单个工单的详细信息和状态变更历史
