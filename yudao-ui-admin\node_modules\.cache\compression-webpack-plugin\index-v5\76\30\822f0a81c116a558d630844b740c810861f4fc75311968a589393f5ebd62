
167e076fd87d6a2545c72d2042a13853cfcca89d	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"46aef802067f3c8a39bf9b517c5cd759\"}","integrity":"sha512-3qoGBgi2csQw5lpzmvEZ12oIB7oe5xvEgVyRYOQMPBzDE+NnXb+zvOTFmk2ghWShNql+kMl36nQyZv35j1jBFg==","time":1754286832799,"size":3474583}