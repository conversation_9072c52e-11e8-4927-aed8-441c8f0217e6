
38f9443b8ae17ed935956117e9da6393819575eb	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.1b0d62968ee0486fe50c.hot-update.js\",\"contentHash\":\"e74d901d8b816a102efc01a18638eede\"}","integrity":"sha512-p/IUe19zkYuX9tR2yeU/5sEAiM7CsIo9mF56sZzI1zXuXmlCI2lObEyfvWS1xTuI2lHdCqoOPGJO3OxQWXcQlg==","time":1754286832328,"size":162238}