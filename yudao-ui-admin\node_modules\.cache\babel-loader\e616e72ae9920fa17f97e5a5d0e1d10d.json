{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\index.vue", "mtime": 1754286907659}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\babel.config.js", "mtime": 1691889630125}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1667694382043}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuaA;;AAoBA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;eACA;EACAA,iBADA;EAEAC;IACAC,iCADA;IAEAC,gCAFA;IAGAC,qBAHA;IAIAC,0BAJA;IAKAC,qBALA;IAMAC,6BANA;IAOAC,2BAPA;IAQAC,+BARA;IASAC;EATA,CAFA;EAaAC,IAbA,kBAaA;IAEA;MACA;MACAC,aAFA;MAGA;MACAC,oBAJA;MAKA;MACAC,gBANA;MAOA;MACAC,QARA;MASA;MACAC,QAVA;MAWA;MACAC,SAZA;MAaA;MACAC,WAdA;MAeAC,6BAfA;MAgBAC,uBAhBA;MAiBA;MACAC;QACAC,SADA;QAEAC,YAFA;QAGAC,UAHA;QAIAxB,UAJA;QAKAyB,kBALA;QAMAC,uBANA;QAOAC,+DAPA;QAQAC,oDARA;QASAC,kBATA;QAUAC;MAVA,CAlBA;MA8BA;MACAC,QA/BA;MAgCA;MACAC;QACAhC;UAAAiC;UAAAC;UAAAC;QAAA,EADA;QAEAV;UAAAQ;UAAAC;UAAAC;QAAA;MAFA,CAjCA;MAqCAC,mBArCA;MAsCAC,sBAtCA;MAuCAC,iBAvCA;MAwCAC,kBAxCA;MAyCAC,iBAzCA;MA0CAC,gBA1CA;MA2CAC,gBA3CA;MA4CAC,gBA5CA;MA6CAjC;QACAQ,WADA;QAEA0B;MAFA,CA7CA;MAiDAxC;QACAwC,gBADA;QAEA5C;MAFA,CAjDA;MAqDA6C,4BArDA;MAsDAC,wBAtDA;MAuDAC,oBAvDA;MAwDAxC;QACAqC;MADA,CAxDA;MA2DAI,iBA3DA;MA4DAJ,gBA5DA;MA6DAK;QACAC,sBADA;QAEAN;MAFA,CA7DA;MAiEAnC;QACAS,WADA;QAEA0B;MAFA,CAjEA;MAqEAO,iBArEA;MAsEA;MACAC;QACA;QACAlC,WAFA;QAGA;QACAD,SAJA;QAKA;QACAoC,kBANA;QAOA;QACAC,sCARA;QASA;QACAC,kGAVA;QAWAC,+GAXA;QAYAC;MAZA,CAvEA;MAqFAC;QACAxC,WADA;QAEAyC,aAFA;QAGAC;MAHA,CArFA;MA0FAC;QACAC,iCADA;QAEAC,6BAFA;QAGAC,iBAHA;QAIAC,2BAJA;QAKAC,sCALA;QAMAC,4BANA;QAOAC,gCAPA;QAQAC;MARA,CA1FA;MAoGAC,yBApGA;MAqGAC,cArGA;MAsGAC,cAtGA;MAuGAb,aAvGA;MAwGA;MACAc;QACAC,cADA;QAEA9D,cAFA;QAGAG,QAHA;QAIAC,QAJA;QAKA2D,aALA;QAMAtD;UACAC,SADA;UAEAC,YAFA;UAGAqD,mBAHA;UAIAC,YAJA;UAKAC,kBALA;UAMAC,oBANA;UAOAC;QAPA;MANA,CAzGA;MAyHA;MACAC;QACAP,cADA;QAEA9D,cAFA;QAGAmB;UACAmD;QADA;MAHA;IA1HA;EAkIA,CAjJA;EAkJAC,OAlJA,qBAkJA;IAAA;;IACA;IACA;MACA;IACA,CAFA;EAGA,CAvJA;EAwJAC;IACAC,gBADA,8BACA;MACA;IACA,CAHA;IAIAC,aAJA,yBAIAC,OAJA,EAIAC,KAJA,EAIAC,GAJA,EAIA;MACA;QACA;UACA;UACA;;QACA;UACA;UACA;;QACA;UACA;UACA;;QACA;UACA;MAXA;IAaA,CAlBA;;IAmBA;IACAC,OApBA,qBAoBA;MAAA;;MACA,oBADA,CAEA;;MACA;MACA;MACA;MACA,wEANA,CAOA;;MACA;QACA;QACA;QACA;MACA,CAJA;IAKA,CAjCA;IAkCAC,SAlCA,uBAkCA;MACA;IACA,CApCA;;IAqCA;IACAC,MAtCA,oBAsCA;MACA;MACA;MACA;MACA;MACA;IACA,CA5CA;;IA6CA;IACAC,KA9CA,mBA8CA;MACA;QACAC,aADA;QAEA9F,eAFA;QAGAyB,uBAHA;QAIAC,4BAJA;QAKAqE,kBALA;QAMAlE,uBANA;QAOAmE,kBAPA;QAQAC,eARA;QASAC,wBATA;QAUAC,kBAVA;QAWAC,mBAXA;QAYAC,2BAZA;QAaAC,qBAbA;QAcAC,sBAdA;QAeAC,mCAfA;QAgBAC,sBAhBA;QAiBAC,2BAjBA;QAkBAC;MAlBA;MAoBA;IACA,CApEA;;IAqEA;IACAC,WAtEA,yBAsEA;MACA;MACA;IACA,CAzEA;;IA0EA;IACAC,UA3EA,wBA2EA;MACA;MACA;MACA;MACA;IACA,CAhFA;;IAiFA;IACAC,SAlFA,uBAkFA;MACA;MACA;MACA;IACA,CAtFA;;IAuFA;IACAC,YAxFA,wBAwFAtB,GAxFA,EAwFA;MAAA;;MACA;MACA;MACA;QACA;QACA;QACA;MACA,CAJA;IAKA,CAhGA;IAiGAuB,eAjGA,2BAiGAvB,GAjGA,EAiGA;MAAA;;MACA;QACA;MACA,CAFA;IAGA,CArGA;IAsGAwB,YAtGA,wBAsGAxB,GAtGA,EAsGA;MACA;MACA;MACA;IACA,CA1GA;IA2GAyB,aA3GA,yBA2GAzB,GA3GA,EA2GA;MAAA;;MACA;QACA;;QACA;UACA,6FADA,CAEA;;UACA,uCACA0B;YAAA;YAAA;YAAA;;YAAA;UAAA,EADA;;UAIA;YACA;YACA;UACA;QACA,CAXA,CAWA;UACAC;UACA;QACA;;QACA;MACA,CAlBA;IAmBA,CA/HA;IAgIAC,QAhIA,sBAgIA;MAAA;;MACA;;MACA;QACA;UACA;UACA;UACAC;UACA;;QACA;UACAA;UACA;;QACA;UACAA;UACA;;QACA;UACA;UACAC;UACAD;UACA;;QACA;UACA;YACAxB,iBADA;YAEAa;UAFA;UAIAW;UACA;;QACA;UACA;YACAxB,iBADA;YAEAa;UAFA;UAIAW;UACA;;QACA;UACAF;MAhCA;;MAkCAE;QACA;QACA;;QACA;;QACA;MACA,CALA;IAMA,CA1KA;IA2KAE,UA3KA,sBA2KA/B,GA3KA,EA2KA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CAlLA;IAmLAgC,YAnLA,wBAmLAhC,GAnLA,EAmLA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CA1LA;IA2LAiC,WA3LA,uBA2LAjC,GA3LA,EA2LA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CAlMA;IAmMAkC,mBAnMA,+BAmMAlC,GAnMA,EAmMA;MACA;MACA;MACA;IACA,CAvMA;IAwMAmC,aAxMA,yBAwMAnC,GAxMA,EAwMA;MACA;MACA;MACA;MACA;MACA;IAEA,CA/MA;IAgNAoC,WAhNA,uBAgNApC,GAhNA,EAgNA;MACA;MACA;MACA;MACA;MACA;IACA,CAtNA;IAuNAqC,YAvNA,wBAuNArC,GAvNA,EAuNA;MAAA;;MACA;QACA;MACA,CAFA,EAEAsC,IAFA,CAEA;QACA;;QACA;MACA,CALA,EAKAC,KALA,CAKA,cALA;IAMA,CA9NA;;IA+NA;IACAC,UAhOA,wBAgOA;MAAA;;MACA;QACA;UACA;QACA,CAHA,CAIA;;;QACA;UACA;YACA;;YACA;;YACA;UACA,CAJA;UAKA;QACA,CAZA,CAaA;;;QACA;UACA;;UACA;;UACA;QACA,CAJA;MAKA,CAnBA;IAoBA,CArPA;;IAsPA;IACAC,YAvPA,wBAuPAzC,GAvPA,EAuPA;MAAA;;MACA;MACA;QACA;MACA,CAFA,EAEAsC,IAFA,CAEA;QACA;;QACA;MACA,CALA,EAKAC,KALA,CAKA,cALA;IAMA,CA/PA;;IAgQA;IACAG,YAjQA,0BAiQA;MAAA;;MACA;MACA;MACAC;MACAA;MACA;MACA,wEANA,CAOA;;MACA;QACA;QACA;MACA,CAHA,EAGAL,IAHA,CAGA;QACA;;QACA;MACA,CANA,EAMAC,KANA,CAMA,cANA;IAOA,CAhRA;IAiRAK,WAjRA,uBAiRA5C,GAjRA,EAiRA;MACA;MACA;QACA7C,uBADA;QAEA5C;MAFA;IAIA,CAvRA;IAwRAsI,WAxRA,uBAwRA7C,GAxRA,EAwRA;MACA;MACA;IACA,CA3RA;IA4RA8C,mBA5RA,+BA4RA9C,GA5RA,EA4RA;MACA;MACA;IACA,CA/RA;IAgSA+C,YAhSA,wBAgSA/C,GAhSA,EAgSA;MACAgD;IACA,CAlSA;IAmSAC,eAnSA,2BAmSAjD,GAnSA,EAmSA;MACA;MACA;QACA7C;MADA;IAGA,CAxSA;IAySA+F,cAzSA,0BAySAlD,GAzSA,EAySA;MACA;QACAvC,qBADA;QAEAN;MAFA;IAIA,CA9SA;IA+SAgG,iBA/SA,6BA+SAnD,GA/SA,EA+SA;MACA;QACAvE,UADA;QAEA0B;MAFA;IAIA,CApTA;IAqTAiG,kCArTA;IAsTAC,gCAtTA;IAuTAC,YAvTA,0BAuTA;MACA;MACA;MACA;IACA,CA3TA;IA4TAC,uBA5TA,qCA4TA;MACA;MACA;MACA;IACA,CAhUA;IAiUA;IACAC,wBAlUA,oCAkUAC,KAlUA,EAkUAC,IAlUA,EAkUAC,QAlUA,EAkUA;MACA;IACA,CApUA;IAqUA;IACAC,iBAtUA,6BAsUAC,QAtUA,EAsUAH,IAtUA,EAsUAC,QAtUA,EAsUA;MACA;QACA;QACA;MACA;;MACA;MACA;MACA;MACAG;MACAA;MACAA;MACAd;MACA;MACA;MACA;MACA;MACA;IACA,CAvVA;IAwVA;IACAe,cAzVA,4BAyVA;MACA;IACA,CA3VA;;IA4VA;IACAC,mBA7VA,+BA6VAhE,GA7VA,EA6VA;MAAA;;MACA;MACA;QACA;;QACA;UACA;;UACA;QACA;;QAEA;UACA,qDADA,CAEA;;UACA,iDACA0B;YAAA;YAAA;YAAA;;YAAA;UAAA,EADA;;UAIA;YACA;;YACA;UACA,CAVA,CAYA;;;UACA;UACA;QACA,CAfA,CAeA;UACAC;;UACA;QACA;MACA,CA1BA,EA0BAY,KA1BA,CA0BA;QACA;MACA,CA5BA;IA6BA,CA5XA;;IA6XA;IACA0B,yBA9XA,qCA8XAlI,IA9XA,EA8XA;MACA;IACA,CAhYA;;IAiYA;IACAmI,iBAlYA,+BAkYA;MACA;MACA;MACA;IACA,CAtYA;;IAuYA;IACAC,uBAxYA,qCAwYA;MACA;MACA;IACA,CA3YA;;IA4YA;IACAC,eA7YA,6BA6YA;MAAA;;MACA,mCADA,CAEA;;MACA;;MACA;QACAzB;QACAA;MACA;;MAEA;QACA;QACA;QACA;MACA,CAJA,EAIAJ,KAJA,CAIA;QACA;MACA,CANA;IAOA,CA7ZA;;IA8ZA;IACA8B,kBA/ZA,gCA+ZA;MACA;MACA;QACAxI,SADA;QAEAC,YAFA;QAGAqD,mBAHA;QAIAC,YAJA;QAKAC,kBALA;QAMAC,oBANA;QAOAC;MAPA;MASA;IACA,CA3aA;;IA4aA;IACA+E,yBA7aA,uCA6aA;MACA;IACA,CA/aA;;IAgbA;IACAC,qBAjbA,iCAibAnF,MAjbA,EAibA;MACA;QACA;UACA;;QACA;UACA;;QACA;UACA;;QACA;UACA;MARA;IAUA,CA5bA;;IA6bA;IACAoF,cA9bA,0BA8bAC,QA9bA,EA8bA;MACA;MAEA;MACA;MACA;;MAEA;QACA;MACA,CAFA,MAEA;QACA;MACA,CAFA,MAEA;QACA;MACA;IACA,CA5cA;;IA6cA;IACAC,iBA9cA,+BA8cA;MACA;MACA;IACA,CAjdA;;IAkdA;IACAC,4BAndA,0CAmdA;MACA;MACA;MACA,8CAHA,CAIA;;MACA;QACA;MACA;IACA,CA3dA;;IA4dA;IACAC,wBA7dA,sCA6dA;MAAA;;MACA;QACA;UACA;QACA,CAHA,CAKA;;;QACA;QACA;;QAEA;UACAC,yBADA;UAEAC,sBAFA;UAGA/I,eAHA;UAIAgJ;QAJA,GAKAzC,IALA,CAKA;UACA;QACA,CAPA,EAOAC,KAPA,CAOA,aACA;QACA,CATA;MAUA,CAnBA;IAoBA,CAlfA;;IAmfA;IACAyC,kBApfA,gCAofA;MAAA;;MACA;MAEA;QACAvF;MADA;MAIA;QACA;;QACA,uCAFA,CAIA;;;QACA;QAAA;QAAA;;QACA,2LANA,CAQA;;;QACA;MACA,CAVA,EAUA8C,KAVA,CAUA;QACA,0CADA,CAEA;;QACAZ;MACA,CAdA;IAeA;EA1gBA;AAxJA,C", "names": ["name", "components", "ImageUpload", "WorkOrderDetail", "ecard", "esign", "order", "household", "bankCard", "personInfo", "disable<PERSON><PERSON>", "data", "loading", "exportLoading", "showSearch", "total", "list", "title", "open", "dateRangeCompensatingDate", "dateRangeCreateTime", "queryParams", "pageNo", "pageSize", "type", "idCardNumber", "mobilePhoneNumber", "treatmentSerialNumberType", "completeStatus", "hospitalName", "types", "form", "rules", "required", "message", "trigger", "detailId", "detailTitle", "detailOpen", "detailOpen2", "method", "ecardOpen", "esignOpen", "orderOpen", "idNum", "confirmBtnText", "confirmBtnLoading", "houseHoldOpen", "opType", "bankAccount", "bankAccountOpen", "hospitalNames", "upload", "isUploading", "headers", "url", "claimAmountUrl", "wandaImportUrl", "supplementary", "activeTab", "files", "supplementaryTypeMap", "MEDICAL_DIAGNOSIS_PROOF", "MEDICAL_FEE_INVOICE", "DRUG_LIST", "MEDICAL_SETTLEMENT", "OUTPATIENT_MEDICAL_RECORDS", "DISCHARGE_RECORD", "DISABILITY_CERTIFICATE", "TRAFFIC_ACCIDENT_CERTIFICATE", "wandaImportLoading", "detailForm", "fileUrlMap", "batchLogDrawer", "visible", "date<PERSON><PERSON><PERSON>", "operationType", "status", "operatorName", "beginStartTime", "endStartTime", "batchRejectDialog", "cutoffDate", "created", "methods", "canInitialFilter", "handleCommand", "command", "index", "row", "getList", "getStatus", "cancel", "reset", "id", "address", "invoice", "bill", "medicalRecord", "summary", "diagnose", "disabilityReport", "deathProof", "adviceMoney", "suggestCompensatingMoney", "actualMoney", "compensatingDate", "remark", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "handleUpdate", "handleCreatePdf", "handleDetail", "handleDetail2", "Object", "console", "doAction", "p", "submitData", "handleTake", "handleReject", "handleDelay", "handleHospitalCheck", "handleProcess", "handleVisit", "handleReturn", "then", "catch", "submitForm", "handleDelete", "handleExport", "params", "handleEcard", "handleOrder", "handleDisablePerson", "handleRecord", "window", "handleHouseHold", "handleBankCard", "handlerPersonInfo", "check<PERSON><PERSON><PERSON>", "checkRole", "handleImport", "handleClaimAmountImport", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "response", "downA", "submitFileForm", "handleSupplementary", "getSupplementaryTypeLabel", "handleWandaImport", "handleBatchOperationLog", "getBatchLogList", "resetBatchLog<PERSON>uery", "handleBatchLogDrawerClose", "getBatchLogStatusType", "formatDuration", "duration", "handleBatchReject", "handleBatchRejectDialogClose", "handleBatchRejectConfirm", "confirmButtonText", "cancelButtonText", "dangerouslyUseHTMLString", "executeBatchReject"], "sourceRoot": "src/views/insurance/workOrder", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n\r\n    <!-- 搜索工作栏 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"90px\">\r\n      <el-form-item label=\"保险类型\">\r\n        <el-select v-model=\"queryParams.type\" placeholder=\"请选择保险类型\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_TYPE)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" v-if=\"(dict.value == '1' && checkPermi(['insurance:old-people-accident-insurance:query'])) || ((dict.value == '7' || dict.value == '8' || dict.value == '9') && checkPermi(['insurance:disabled-people-insurance:query']))\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"医院名称\">\r\n        <el-select v-model=\"queryParams.hospitalName\" placeholder=\"请选择医院\" clearable>\r\n          <el-option\r\n            v-for=\"item in hospitalNames\"\r\n            :key=\"item\"\r\n            :label=\"item\"\r\n            :value=\"item\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"长者名称\" prop=\"name\">\r\n        <el-input v-model=\"queryParams.name\" placeholder=\"请输入长者名称\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"身份证号\" prop=\"idCardNumber\">\r\n        <el-input v-model=\"queryParams.idCardNumber\" placeholder=\"请输入身份证号\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系方式\" prop=\"mobilePhoneNumber\">\r\n        <el-input v-model=\"queryParams.mobilePhoneNumber\" placeholder=\"请输入联系方式\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"理赔时间\">\r\n        <el-date-picker v-model=\"dateRangeCompensatingDate\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n                        type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"工单类型\">\r\n        <el-select v-model=\"queryParams.treatmentSerialNumberType\" placeholder=\"请选择工单类型\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_TREATMENT_TYPE)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"票据完整度\">\r\n        <el-select v-model=\"queryParams.completeStatus\" placeholder=\"请选择完整度\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_WORK_ORDER_TICKET_COMPLETE_STATUS)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRangeCreateTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作工具栏 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n                   v-hasPermi=\"['insurance:work-order:create']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" :loading=\"exportLoading\"\r\n                   v-hasPermi=\"['insurance:work-order:export']\">导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleImport\"\r\n                   v-hasPermi=\"['insurance:work-order:settlement-import']\">已理赔工单导入</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleClaimAmountImport\"\r\n                   v-hasPermi=\"['insurance:work-order:claim-import']\">理赔金额导入</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" size=\"mini\" icon=\"el-icon-upload2\"\r\n                   @click=\"handleWandaImport\" :loading=\"wandaImportLoading\"\r\n                   v-hasPermi=\"['insurance:work-order:import']\">万达医疗数据更新</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-document\" size=\"mini\"\r\n                   @click=\"handleBatchOperationLog\"\r\n                   v-hasPermi=\"['insurance:work-order:query']\">批量操作日志</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\"\r\n                   @click=\"handleBatchReject\"\r\n                   v-hasPermi=\"['insurance:work-order:batch-reject']\">批量拒绝旧工单</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"list\">\r\n      <el-table-column label=\"编号\" align=\"center\" type=\"index\" width=\"50px\"/>\r\n      <el-table-column label=\"保险类型\" align=\"center\" prop=\"type\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TYPE\" :value=\"scope.row.type\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"工单类型\" align=\"center\" prop=\"treatmentSerialNumberType\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TREATMENT_TYPE\" :value=\"scope.row.treatmentSerialNumberType\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column v-if=\"checkPermi(['insurance:work-order:show-company'])\" label=\"负责公司\" align=\"center\" prop=\"companyName\" />\r\n      <el-table-column label=\"长者名称\" align=\"center\" prop=\"desensitizedName\" />\r\n      <el-table-column label=\"身份证号\" align=\"center\" prop=\"desensitizedIdCardNumber\" />\r\n      <el-table-column label=\"联系方式\" align=\"center\" prop=\"desensitizedMobilePhoneNumber\" />\r\n      <el-table-column label=\"医院\" align=\"center\" prop=\"hospitalName\" />\r\n      <el-table-column label=\"就诊时间\" align=\"center\" prop=\"treatmentDatetime\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.treatmentDatetime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"建议理赔金额\" align=\"center\" width=\"100\" prop=\"suggestCompensatingMoney\" />\r\n      <el-table-column label=\"赔付金额\" align=\"center\" prop=\"actualMoney\" />\r\n      <el-table-column label=\"票据完整度\" align=\"center\" prop=\"completeStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_WORK_ORDER_TICKET_COMPLETE_STATUS\" :value=\"scope.row.completeStatus\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"险种\" align=\"center\" prop=\"completeStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TYPE\" :value=\"scope.row.type\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"授权状态\" align=\"center\" prop=\"authStatus\" />\r\n      <el-table-column label=\"理赔申请\" align=\"center\" prop=\"supplementaryFileRecordId\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.supplementaryFileRecordId == null\">否</el-tag>\r\n          <el-tag v-else type=\"info\">是</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column fixed=\"right\" label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"300px\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleEcard(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:cert'])\">电子证照</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleOrder(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:ticket'])\">电子保单</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleHouseHold(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:house'])\">电子户口</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleBankCard(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:bankcard'])\">银行账号</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handlerPersonInfo(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:query'])\">联系方式</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleDisablePerson(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:query'])\">残疾人证</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleRecord(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:medical']) && scope.row.status > 1\">就诊证明</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"if(!scope.row.supplementaryFileRecordId) { handleDetail(scope.row); } else { handleDetail2(scope.row); }\"\r\n                    v-if=\"checkPermi(['insurance:work-order:query'])\">详情</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleTake(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:take']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">接单</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleReject(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:reject']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">拒绝</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleDelay(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:delay']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">延后</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleHospitalCheck(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:hospital-check']) && scope.row.status === 1 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">盖章</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleProcess(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:process']) && scope.row.status === 2 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">处理</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleVisit(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:visit']) && scope.row.status === 3 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">回访</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleReturn(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:return']) && (scope.row.status === 2 || scope.row.status === 3 || scope.row.status === 6) && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">回退</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleSupplementary(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:query'])\">补充资料</el-button>\r\n          <el-dropdown  @command=\"(command) => handleCommand(command, scope.$index, scope.row)\"\r\n                        v-if=\"queryParams.status != null\"\r\n                        v-hasPermi=\"['insurance:work-order:update', 'insurance:work-order:delete']\">\r\n                <span class=\"el-dropdown-link\">\r\n                  <i class=\"el-icon-d-arrow-right el-icon--right\"></i>更多\r\n                </span>\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <el-dropdown-item command=\"handleDelete\" size=\"mini\" type=\"text\" icon=\"el-icon-delete\"\r\n                                v-hasPermi=\"['insurance:work-order:delete']\">删除</el-dropdown-item>\r\n              <el-dropdown-item command=\"handleCreatePdf\" size=\"mini\" type=\"text\" icon=\"el-icon-edit\"\r\n                                v-hasPermi=\"['insurance:work-order:update']\">创建未签章pdf</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <!-- 分页组件 -->\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNo\" :limit.sync=\"queryParams.pageSize\"\r\n                @pagination=\"getList\"/>\r\n\r\n    <el-drawer :title=\"detailTitle\" :visible.sync=\"detailOpen\" direction=\"rtl\" size=\"60%\">\r\n      <WorkOrderDetail ref=\"workOrderDetail\" :opType=\"opType\" v-if=\"detailOpen\" :id=\"detailId\" />\r\n      <div class=\"drawer-footer\" v-if=\"detailTitle !== '查看详情'\">\r\n        <el-button type=\"primary\" @click=\"doAction\" :loading=\"confirmBtnLoading\">{{confirmBtnText}}</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-drawer>\r\n    <el-drawer title=\"电子签章\" :visible.sync=\"esignOpen\" direction=\"rtl\" size=\"90%\">\r\n      <esign />\r\n      <div class=\"drawer-footer\">\r\n        <el-button type=\"primary\" @click=\"doAction\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog title=\"电子证照\" :visible.sync=\"ecardOpen\" width=\"500px\">\r\n      <ecard :idNum=\"ecard.idNum\" :name=\"ecard.name\" />\r\n    </el-dialog>\r\n    <el-drawer title=\"保单详情\" :visible.sync=\"orderOpen\" direction=\"rtl\" size=\"90%\">\r\n      <order :idNum=\"idNum\"/>\r\n    </el-drawer>\r\n    <el-drawer title=\"残疾人证\" :visible.sync=\"disablePerson.open\" direction=\"rtl\" size=\"90%\">\r\n      <disablePerson :idNum=\"disablePerson.idNum\"/>\r\n    </el-drawer>\r\n    <el-dialog title=\"电子户口\" :visible.sync=\"houseHoldOpen\" width=\"550px\">\r\n      <household :idNum=\"household.idNum\" />\r\n    </el-dialog>\r\n    <el-dialog title=\"银行账号\" :visible.sync=\"bankAccount.bankAccountOpen\" width=\"550px\">\r\n      <bankCard :idCard=\"bankAccount.idNum\" />\r\n    </el-dialog>\r\n    <el-dialog title=\"多来源信息\" :visible.sync=\"personInfo.open\" width=\"550px\">\r\n      <personInfo :idCard=\"personInfo.idNum\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\r\n      <el-upload ref=\"upload\" :limit=\"1\" accept=\".xlsx, .xls\" :headers=\"upload.headers\"\r\n        :action=\"upload.url\" :disabled=\"upload.isUploading\"\r\n        :on-progress=\"handleFileUploadProgress\" :on-success=\"handleFileSuccess\" :auto-upload=\"false\" drag>\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\r\n          <span>仅允许导入xls、xlsx格式文件。</span>\r\n        </div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"upload.open = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"补充资料\" :visible.sync=\"supplementary.open\" width=\"800px\" append-to-body>\r\n      <el-tabs v-model=\"supplementary.activeTab\">\r\n        <el-tab-pane v-for=\"(urls, type) in supplementary.files\"\r\n                     :key=\"type\"\r\n                     :label=\"getSupplementaryTypeLabel(type)\"\r\n                     :name=\"type\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"8\" v-for=\"(url, index) in urls\" :key=\"index\">\r\n              <el-image\r\n                style=\"width: 100%; height: 200px\"\r\n                :src=\"url\"\r\n                :preview-src-list=\"urls\">\r\n              </el-image>\r\n            </el-col>\r\n          </el-row>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n\r\n    <!-- 详情对话框 -->\r\n    <el-dialog :title=\"'补充资料详情'\" :visible.sync=\"detailOpen2\" width=\"800px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"居民\">{{ detailForm.residentName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"状态\">\r\n          <dict-tag :type=\"DICT_TYPE.SUPPLEMENTARY_RECORD_STATUS\" :value=\"detailForm.status\"/>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"户籍地址\">{{ detailForm.hjAddress }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"手机号\">{{ detailForm.phone }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"实际理赔金额\" v-if=\"detailForm.actualMoney\">\r\n          {{ detailForm.actualMoney }}元\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <el-divider content-position=\"left\">补充资料</el-divider>\r\n      <el-tabs v-model=\"activeTab\">\r\n        <el-tab-pane\r\n          v-for=\"(urls, type) in fileUrlMap\"\r\n          :key=\"type\"\r\n          :label=\"getSupplementaryTypeLabel(type)\"\r\n          :name=\"type\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"8\" v-for=\"(url, index) in urls\" :key=\"index\">\r\n              <el-image\r\n                style=\"width: 100%; height: 200px\"\r\n                :src=\"url\"\r\n                :preview-src-list=\"urls\">\r\n              </el-image>\r\n            </el-col>\r\n          </el-row>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n\r\n    <!-- 批量操作日志抽屉 -->\r\n    <el-drawer\r\n      title=\"批量操作日志\"\r\n      :visible.sync=\"batchLogDrawer.visible\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :before-close=\"handleBatchLogDrawerClose\">\r\n\r\n      <!-- 搜索条件 -->\r\n      <el-form :model=\"batchLogDrawer.queryParams\" ref=\"batchLogQueryForm\" size=\"small\" :inline=\"true\" label-width=\"100px\" style=\"margin-bottom: 20px;\">\r\n        <el-form-item label=\"操作类型\">\r\n          <el-select v-model=\"batchLogDrawer.queryParams.operationType\" placeholder=\"请选择操作类型\" clearable>\r\n            <el-option label=\"批量拒绝\" value=\"BATCH_REJECT\"></el-option>\r\n            <el-option label=\"批量恢复\" value=\"BATCH_RECOVER\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"操作状态\">\r\n          <el-select v-model=\"batchLogDrawer.queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n            <el-option label=\"执行中\" value=\"RUNNING\"></el-option>\r\n            <el-option label=\"已完成\" value=\"COMPLETED\"></el-option>\r\n            <el-option label=\"执行失败\" value=\"FAILED\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"操作员\">\r\n          <el-input v-model=\"batchLogDrawer.queryParams.operatorName\" placeholder=\"请输入操作员姓名\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"操作时间\">\r\n          <el-date-picker\r\n            v-model=\"batchLogDrawer.dateRange\"\r\n            style=\"width: 240px\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            type=\"datetimerange\"\r\n            range-separator=\"-\"\r\n            start-placeholder=\"开始时间\"\r\n            end-placeholder=\"结束时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getBatchLogList\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" @click=\"resetBatchLogQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 批量操作日志表格 -->\r\n      <el-table v-loading=\"batchLogDrawer.loading\" :data=\"batchLogDrawer.list\" style=\"width: 100%\">\r\n        <el-table-column label=\"批次号\" align=\"center\" prop=\"batchId\" width=\"180\" show-overflow-tooltip></el-table-column>\r\n        <el-table-column label=\"操作类型\" align=\"center\" prop=\"operationTypeDisplay\" width=\"100\"></el-table-column>\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"statusDisplay\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getBatchLogStatusType(scope.row.status)\">{{ scope.row.statusDisplay }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"处理数量\" align=\"center\" prop=\"processedCount\" width=\"80\"></el-table-column>\r\n        <el-table-column label=\"操作员\" align=\"center\" prop=\"operatorName\" width=\"100\"></el-table-column>\r\n        <el-table-column label=\"开始时间\" align=\"center\" prop=\"startTime\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"结束时间\" align=\"center\" prop=\"endTime\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.endTime ? parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"执行时长\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatDuration(scope.row.duration) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remarks\" show-overflow-tooltip></el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <pagination\r\n        v-show=\"batchLogDrawer.total > 0\"\r\n        :total=\"batchLogDrawer.total\"\r\n        :page.sync=\"batchLogDrawer.queryParams.pageNo\"\r\n        :limit.sync=\"batchLogDrawer.queryParams.pageSize\"\r\n        @pagination=\"getBatchLogList\"\r\n        style=\"margin-top: 20px;\" />\r\n    </el-drawer>\r\n\r\n    <!-- 批量拒绝模态框 -->\r\n    <el-dialog\r\n      title=\"批量拒绝历史待接单工单\"\r\n      :visible.sync=\"batchRejectDialog.visible\"\r\n      width=\"500px\"\r\n      :before-close=\"handleBatchRejectDialogClose\">\r\n\r\n      <div style=\"margin-bottom: 20px;\">\r\n        <p style=\"color: #606266; line-height: 1.6;\">\r\n          此操作将批量拒绝指定日期及之前的所有待接单工单。被拒绝的工单状态将变更为\"行政拒绝\"，\r\n          此操作可通过批量操作日志进行恢复。请谨慎操作。\r\n        </p>\r\n      </div>\r\n\r\n      <el-form :model=\"batchRejectDialog.form\" ref=\"batchRejectForm\" label-width=\"120px\">\r\n        <el-form-item label=\"截止日期\" prop=\"cutoffDate\"\r\n                      :rules=\"[{ required: true, message: '请选择截止日期', trigger: 'change' }]\">\r\n          <el-date-picker\r\n            v-model=\"batchRejectDialog.form.cutoffDate\"\r\n            type=\"date\"\r\n            placeholder=\"选择截止日期\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            style=\"width: 100%\">\r\n          </el-date-picker>\r\n          <div style=\"font-size: 12px; color: #909399; margin-top: 5px;\">\r\n            将拒绝此日期及之前的所有待接单工单\r\n          </div>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleBatchRejectDialogClose\">取 消</el-button>\r\n        <el-button type=\"danger\" @click=\"handleBatchRejectConfirm\" :loading=\"batchRejectDialog.loading\">执 行</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  createWorkOrder,\r\n  updateWorkOrder,\r\n  deleteWorkOrder,\r\n  getWorkOrder,\r\n  getWorkOrderPage,\r\n  exportWorkOrderExcel,\r\n  takeWorkOrder,\r\n  hospitalCheck,\r\n  process as process2,\r\n  visit,\r\n  reject,\r\n  delay,\r\n  returnWorkOrder,\r\n  getHospitalNames,\r\n  createPdf,\r\n  getBatchOperationLogPage,\r\n  batchRejectWorkOrders\r\n}\r\nfrom \"@/api/insurance/workOrder\";\r\nimport {getSupplementaryFileRecord} from \"@/api/insurance/supplementaryFileRecord\";\r\nimport ImageUpload from '@/components/ImageUpload';\r\nimport { checkPermi, checkRole } from \"@/utils/permission\";\r\nimport WorkOrderDetail from \"./detail\"\r\nimport ecard from '../components/ecard.vue';\r\nimport esign from '../components/esignature.vue';\r\nimport order from '../components/order.vue';\r\nimport disablePerson from '../components/disablePerson.vue';\r\nimport household from '../components/household.vue';\r\nimport bankCard from '../components/bankCard.vue';\r\nimport personInfo from '../components/personInfo.vue';\r\nimport {getBaseHeader} from \"@/utils/request\";\r\nconst CONFIRM_TEXT = '确 定';\r\nexport default {\r\n  name: \"WorkOrder\",\r\n  components: {\r\n    ImageUpload,\r\n    WorkOrderDetail,\r\n    ecard,\r\n    esign,\r\n    order,\r\n    household,\r\n    bankCard,\r\n    personInfo,\r\n    disablePerson,\r\n  },\r\n  data() {\r\n\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 导出遮罩层\r\n      exportLoading: false,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 工单列表\r\n      list: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      dateRangeCompensatingDate: [],\r\n      dateRangeCreateTime: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        type: null,\r\n        name: null,\r\n        idCardNumber: null,\r\n        mobilePhoneNumber: null,\r\n        treatmentSerialNumberType: this.canInitialFilter()? '1': null,\r\n        completeStatus: this.canInitialFilter()? '0': null,\r\n        hospitalName: null,\r\n        types: [1, 7, 8, 9],\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        name: [{ required: true, message: \"长者名称不能为空\", trigger: \"blur\" }],\r\n        idCardNumber: [{ required: true, message: \"身份证号不能为空\", trigger: \"blur\" }],\r\n      },\r\n      detailId: undefined,\r\n      detailTitle: undefined,\r\n      detailOpen: false,\r\n      detailOpen2: false,\r\n      method: undefined,\r\n      ecardOpen: false,\r\n      esignOpen: false,\r\n      orderOpen: false,\r\n      disablePerson: {\r\n        open: false,\r\n        idNum: undefined,\r\n      },\r\n      ecard: {\r\n        idNum: undefined,\r\n        name: undefined\r\n      },\r\n      confirmBtnText: CONFIRM_TEXT,\r\n      confirmBtnLoading: false,\r\n      houseHoldOpen: false,\r\n      household: {\r\n        idNum: undefined,\r\n      },\r\n      opType: undefined,\r\n      idNum: undefined,\r\n      bankAccount: {\r\n        bankAccountOpen: false,\r\n        idNum: undefined\r\n      },\r\n      personInfo: {\r\n        open: false,\r\n        idNum: undefined\r\n      },\r\n      hospitalNames: [],\r\n      //已赔付工单导入\r\n      upload: {\r\n        // 是否显示弹出层（用户导入）\r\n        open: false,\r\n        // 弹出层标题（用户导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: getBaseHeader(),\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-settlement-work-order',\r\n        claimAmountUrl: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-claim-amount-work-order',\r\n        wandaImportUrl: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-wanda-data'\r\n      },\r\n      supplementary: {\r\n        open: false,\r\n        activeTab: '',\r\n        files: {},\r\n      },\r\n      supplementaryTypeMap: {\r\n        MEDICAL_DIAGNOSIS_PROOF: '医疗诊断证明',\r\n        MEDICAL_FEE_INVOICE: '医疗费用发票',\r\n        DRUG_LIST: '用药清单',\r\n        MEDICAL_SETTLEMENT: '医保结算单',\r\n        OUTPATIENT_MEDICAL_RECORDS: '门诊病历(门诊)',\r\n        DISCHARGE_RECORD: '出院记录(住院)',\r\n        DISABILITY_CERTIFICATE: '残疾鉴定报告',\r\n        TRAFFIC_ACCIDENT_CERTIFICATE: '交通事故责任认定书'\r\n      },\r\n      wandaImportLoading: false,\r\n      detailForm: {},\r\n      fileUrlMap: {},\r\n      activeTab: '',\r\n      // 批量操作日志抽屉\r\n      batchLogDrawer: {\r\n        visible: false,\r\n        loading: false,\r\n        total: 0,\r\n        list: [],\r\n        dateRange: [],\r\n        queryParams: {\r\n          pageNo: 1,\r\n          pageSize: 10,\r\n          operationType: null,\r\n          status: null,\r\n          operatorName: null,\r\n          beginStartTime: null,\r\n          endStartTime: null\r\n        }\r\n      },\r\n      // 批量拒绝对话框\r\n      batchRejectDialog: {\r\n        visible: false,\r\n        loading: false,\r\n        form: {\r\n          cutoffDate: null\r\n        }\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    getHospitalNames().then(response => {\r\n      this.hospitalNames = response.data;\r\n    })\r\n  },\r\n  methods: {\r\n    canInitialFilter() {\r\n      return this.checkRole(['insurance', 'mz']);\r\n    },\r\n    handleCommand(command, index, row) {\r\n      switch (command) {\r\n        case 'handleUpdate':\r\n          this.handleUpdate(row);\r\n          break;\r\n        case 'handleDelete':\r\n          this.handleDelete(row);\r\n          break;\r\n        case 'handleCreatePdf':\r\n          this.handleCreatePdf(row);\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    /** 查询列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 处理查询参数\r\n      this.queryParams.status = this.getStatus();\r\n      let params = {...this.queryParams};\r\n      this.addBeginAndEndTime(params, this.dateRangeCompensatingDate, 'compensatingDate');\r\n      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');\r\n      // 执行查询\r\n      getWorkOrderPage(params).then(response => {\r\n        this.list = response.data.list;\r\n        this.total = response.data.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    getStatus() {\r\n      return this.$route.query.status\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.detailOpen = false;\r\n      this.detailOpen2 = false;\r\n      this.esignOpen = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: undefined,\r\n        name: undefined,\r\n        idCardNumber: undefined,\r\n        mobilePhoneNumber: undefined,\r\n        address: undefined,\r\n        hospitalName: undefined,\r\n        invoice: undefined,\r\n        bill: undefined,\r\n        medicalRecord: undefined,\r\n        summary: undefined,\r\n        diagnose: undefined,\r\n        disabilityReport: undefined,\r\n        deathProof: undefined,\r\n        adviceMoney: undefined,\r\n        suggestCompensatingMoney: undefined,\r\n        actualMoney: undefined,\r\n        compensatingDate: undefined,\r\n        remark: undefined,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNo = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRangeCompensatingDate = [];\r\n      this.dateRangeCreateTime = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加工单\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id;\r\n      getWorkOrder(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改工单\";\r\n      });\r\n    },\r\n    handleCreatePdf(row) {\r\n      createPdf(row.id).then(response => {\r\n        this.getList();\r\n      });\r\n    },\r\n    handleDetail(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"查看详情\";\r\n    },\r\n    handleDetail2(row) {\r\n      getSupplementaryFileRecord(row.supplementaryFileRecordId).then(response => {\r\n        this.detailForm = response.data;\r\n        try {\r\n          this.fileUrlMap = this.detailForm.fileUrls ? JSON.parse(this.detailForm.fileUrls) : {};\r\n          // 过滤掉没有图片的类型\r\n          this.fileUrlMap = Object.fromEntries(\r\n            Object.entries(this.fileUrlMap).filter(([_, urls]) => urls && urls.length > 0)\r\n          );\r\n\r\n          if (Object.keys(this.fileUrlMap).length > 0) {\r\n            // 设置第一个标签为激活状态\r\n            this.activeTab = Object.keys(this.fileUrlMap)[0];\r\n          }\r\n        } catch (e) {\r\n          console.error('解析 fileUrls 失败:', e);\r\n          this.fileUrlMap = {};\r\n        }\r\n        this.detailOpen2 = true;\r\n      });\r\n    },\r\n    doAction() {\r\n      let p = undefined;\r\n      switch(this.method) {\r\n        case 'take':\r\n          this.confirmBtnText = '正在签章,请稍候';\r\n          this.confirmBtnLoading = true;\r\n          p = takeWorkOrder(this.detailId);\r\n          break;\r\n        case 'hospital':\r\n          p = hospitalCheck(this.detailId);\r\n          break;\r\n        case 'process':\r\n          p = process2(this.detailId);\r\n          break;\r\n        case 'visit':\r\n          let submitData = this.$refs.workOrderDetail.getSubmitData()\r\n          submitData.id = this.detailId\r\n          p = visit(submitData);\r\n          break;\r\n        case 'reject':\r\n          let rejectData = {\r\n            id: this.detailId,\r\n            remark: this.$refs.workOrderDetail.getSubmitData().remark\r\n          }\r\n          p = reject(rejectData);\r\n          break;\r\n        case 'delay':\r\n          let delayData = {\r\n            id: this.detailId,\r\n            remark: this.$refs.workOrderDetail.getSubmitData().remark\r\n          }\r\n          p = delay(delayData);\r\n          break;\r\n        default:\r\n          console.log('找不到对应方法: ' + this.method);\r\n      }\r\n      p.then(() => {\r\n        this.confirmBtnLoading = false;\r\n        this.confirmBtnText = CONFIRM_TEXT;\r\n        this.cancel();\r\n        this.getList();\r\n      });\r\n    },\r\n    handleTake(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"接单\";\r\n      this.method = 'take';\r\n      this.confirmBtnText = '接 单';\r\n      this.opType = 'take';\r\n    },\r\n    handleReject(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"拒绝\";\r\n      this.method = 'reject';\r\n      this.confirmBtnText = '拒绝';\r\n      this.opType = 'reject';\r\n    },\r\n    handleDelay(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"延后\";\r\n      this.method = 'delay';\r\n      this.confirmBtnText = '延 后';\r\n      this.opType = 'delay';\r\n    },\r\n    handleHospitalCheck(row) {\r\n      this.detailId = row.id;\r\n      this.esignOpen = true;\r\n      this.method = 'hospital';\r\n    },\r\n    handleProcess(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"处理\";\r\n      this.method = 'process';\r\n      this.opType = 'process';\r\n\r\n    },\r\n    handleVisit(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"回访\";\r\n      this.method = 'visit';\r\n      this.opType = 'visit';\r\n    },\r\n    handleReturn(row) {\r\n      this.$modal.confirm(`是否确认回退工单(${row.name})`).then(function() {\r\n          return returnWorkOrder(row.id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"回退成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (!valid) {\r\n          return;\r\n        }\r\n        // 修改的提交\r\n        if (this.form.id != null) {\r\n          updateWorkOrder(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.getList();\r\n          });\r\n          return;\r\n        }\r\n        // 添加的提交\r\n        createWorkOrder(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"新增成功\");\r\n          this.open = false;\r\n          this.getList();\r\n        });\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const id = row.id;\r\n      this.$modal.confirm('是否确认删除工单编号为\"' + id + '\"的数据项?').then(function() {\r\n          return deleteWorkOrder(id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      params.pageNo = undefined;\r\n      params.pageSize = undefined;\r\n      this.addBeginAndEndTime(params, this.dateRangeCompensatingDate, 'compensatingDate');\r\n      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');\r\n      // 执行导出\r\n      this.$modal.confirm('是否确认导出所有工单数据项?').then(() => {\r\n          this.exportLoading = true;\r\n          return exportWorkOrderExcel(params);\r\n        }).then(response => {\r\n          this.$download.excel(response, '工单.xls');\r\n          this.exportLoading = false;\r\n        }).catch(() => {});\r\n    },\r\n    handleEcard(row) {\r\n      this.ecardOpen = true;\r\n      this.ecard = {\r\n        idNum: row.idCardNumber,\r\n        name: row.name\r\n      }\r\n    },\r\n    handleOrder(row) {\r\n      this.orderOpen = true;\r\n      this.idNum = row.idCardNumber\r\n    },\r\n    handleDisablePerson(row) {\r\n      this.disablePerson.open = true;\r\n      this.disablePerson.idNum = row.idCardNumber;\r\n    },\r\n    handleRecord(row) {\r\n      window.open(row.pdf.signedPdf, \"_blank\", \"resizable,scrollbars,status\");\r\n    },\r\n    handleHouseHold(row) {\r\n      this.houseHoldOpen = true;\r\n      this.household = {\r\n        idNum: row.idCardNumber,\r\n      }\r\n    },\r\n    handleBankCard(row) {\r\n      this.bankAccount = {\r\n        bankAccountOpen: true,\r\n        idNum: row.idCardNumber,\r\n      }\r\n    },\r\n    handlerPersonInfo(row) {\r\n      this.personInfo = {\r\n        open: true,\r\n        idNum: row.idCardNumber,\r\n      }\r\n    },\r\n    checkPermi,\r\n    checkRole,\r\n    handleImport() {\r\n      this.upload.title = \"工单导入\";\r\n      this.upload.open = true;\r\n      this.upload.url = this.upload.url;\r\n    },\r\n    handleClaimAmountImport() {\r\n      this.upload.title = \"理赔金额导入\";\r\n      this.upload.open = true;\r\n      this.upload.url = this.upload.claimAmountUrl;\r\n    },\r\n    // 文件上传中处理\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true;\r\n    },\r\n    // 文件上传成功处理\r\n    handleFileSuccess(response, file, fileList) {\r\n      if (response.code !== 0) {\r\n        this.$modal.msgError(response.msg)\r\n        return;\r\n      }\r\n      let fileName = file.name;\r\n      let href = response.data;\r\n      let downA = document.createElement(\"a\");\r\n      downA.href = href;\r\n      downA.download = fileName;\r\n      downA.click();\r\n      window.URL.revokeObjectURL(href);\r\n      this.upload.open = false;\r\n      this.upload.isUploading = false;\r\n      this.$refs.upload.clearFiles();\r\n      this.$modal.msgSuccess(\"导入成功，请查看导入结果文件\");\r\n      this.getList();\r\n    },\r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit();\r\n    },\r\n    /** 处理补充资料按钮点击 */\r\n    handleSupplementary(row) {\r\n      // 先获取工单详情\r\n      getWorkOrder(row.id).then(response => {\r\n        const workOrder = response.data;\r\n        if (!workOrder.supplementaryFiles) {\r\n          this.$modal.msgError(\"没有补充资料\");\r\n          return;\r\n        }\r\n\r\n        try {\r\n          const files = JSON.parse(workOrder.supplementaryFiles);\r\n          // 过滤掉没有图片的类型\r\n          this.supplementary.files = Object.fromEntries(\r\n            Object.entries(files).filter(([_, urls]) => urls && urls.length > 0)\r\n          );\r\n\r\n          if (Object.keys(this.supplementary.files).length === 0) {\r\n            this.$modal.msgError(\"没有补充资料\");\r\n            return;\r\n          }\r\n\r\n          // 设置第一个标签为激活状态\r\n          this.supplementary.activeTab = Object.keys(this.supplementary.files)[0];\r\n          this.supplementary.open = true;\r\n        } catch (e) {\r\n          console.error(\"解析补充资料失败\", e);\r\n          this.$modal.msgError(\"解析补充资料失败\");\r\n        }\r\n      }).catch(() => {\r\n        this.$modal.msgError(\"获取工单详情失败\");\r\n      });\r\n    },\r\n    /** 获取补充资料类型的显示文本 */\r\n    getSupplementaryTypeLabel(type) {\r\n      return this.supplementaryTypeMap[type] || type;\r\n    },\r\n    /** 万达数据导入按钮操作 */\r\n    handleWandaImport() {\r\n      this.upload.title = \"万达数据导入\";\r\n      this.upload.open = true;\r\n      this.upload.url = this.upload.wandaImportUrl;\r\n    },\r\n    /** 批量操作日志按钮操作 */\r\n    handleBatchOperationLog() {\r\n      this.batchLogDrawer.visible = true;\r\n      this.getBatchLogList();\r\n    },\r\n    /** 获取批量操作日志列表 */\r\n    getBatchLogList() {\r\n      this.batchLogDrawer.loading = true;\r\n      // 处理时间范围参数\r\n      let params = { ...this.batchLogDrawer.queryParams };\r\n      if (this.batchLogDrawer.dateRange && this.batchLogDrawer.dateRange.length === 2) {\r\n        params.beginStartTime = this.batchLogDrawer.dateRange[0];\r\n        params.endStartTime = this.batchLogDrawer.dateRange[1];\r\n      }\r\n\r\n      getBatchOperationLogPage(params).then(response => {\r\n        this.batchLogDrawer.list = response.data.list;\r\n        this.batchLogDrawer.total = response.data.total;\r\n        this.batchLogDrawer.loading = false;\r\n      }).catch(() => {\r\n        this.batchLogDrawer.loading = false;\r\n      });\r\n    },\r\n    /** 重置批量操作日志查询 */\r\n    resetBatchLogQuery() {\r\n      this.batchLogDrawer.dateRange = [];\r\n      this.batchLogDrawer.queryParams = {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        operationType: null,\r\n        status: null,\r\n        operatorName: null,\r\n        beginStartTime: null,\r\n        endStartTime: null\r\n      };\r\n      this.getBatchLogList();\r\n    },\r\n    /** 关闭批量操作日志抽屉 */\r\n    handleBatchLogDrawerClose() {\r\n      this.batchLogDrawer.visible = false;\r\n    },\r\n    /** 获取批量操作状态标签类型 */\r\n    getBatchLogStatusType(status) {\r\n      switch (status) {\r\n        case 'RUNNING':\r\n          return 'warning';\r\n        case 'COMPLETED':\r\n          return 'success';\r\n        case 'FAILED':\r\n          return 'danger';\r\n        default:\r\n          return 'info';\r\n      }\r\n    },\r\n    /** 格式化执行时长 */\r\n    formatDuration(duration) {\r\n      if (!duration) return '-';\r\n\r\n      const seconds = Math.floor(duration / 1000);\r\n      const minutes = Math.floor(seconds / 60);\r\n      const hours = Math.floor(minutes / 60);\r\n\r\n      if (hours > 0) {\r\n        return `${hours}小时${minutes % 60}分${seconds % 60}秒`;\r\n      } else if (minutes > 0) {\r\n        return `${minutes}分${seconds % 60}秒`;\r\n      } else {\r\n        return `${seconds}秒`;\r\n      }\r\n    },\r\n    /** 批量拒绝按钮操作 */\r\n    handleBatchReject() {\r\n      this.batchRejectDialog.visible = true;\r\n      this.batchRejectDialog.form.cutoffDate = null;\r\n    },\r\n    /** 关闭批量拒绝对话框 */\r\n    handleBatchRejectDialogClose() {\r\n      this.batchRejectDialog.visible = false;\r\n      this.batchRejectDialog.loading = false;\r\n      this.batchRejectDialog.form.cutoffDate = null;\r\n      // 清除表单验证\r\n      if (this.$refs.batchRejectForm) {\r\n        this.$refs.batchRejectForm.clearValidate();\r\n      }\r\n    },\r\n    /** 批量拒绝确认操作 */\r\n    handleBatchRejectConfirm() {\r\n      this.$refs.batchRejectForm.validate(valid => {\r\n        if (!valid) {\r\n          return;\r\n        }\r\n\r\n        // 二次确认对话框\r\n        const cutoffDate = this.batchRejectDialog.form.cutoffDate;\r\n        const confirmMessage = `您确定要拒绝 ${cutoffDate} 及之前的所有待接单工单吗？此操作可通过日志恢复。`;\r\n\r\n        this.$modal.confirm(confirmMessage, '批量拒绝确认', {\r\n          confirmButtonText: '确定执行',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n          dangerouslyUseHTMLString: false\r\n        }).then(() => {\r\n          this.executeBatchReject();\r\n        }).catch(() => {\r\n          // 用户取消操作\r\n        });\r\n      });\r\n    },\r\n    /** 执行批量拒绝操作 */\r\n    executeBatchReject() {\r\n      this.batchRejectDialog.loading = true;\r\n\r\n      const requestData = {\r\n        cutoffDate: this.batchRejectDialog.form.cutoffDate + ' 23:59:59'\r\n      };\r\n\r\n      batchRejectWorkOrders(requestData).then(response => {\r\n        this.batchRejectDialog.loading = false;\r\n        this.handleBatchRejectDialogClose();\r\n\r\n        // 显示成功提示\r\n        const { batchId, processedCount } = response.data;\r\n        this.$modal.msgSuccess(`操作成功！批次号：${batchId}，共处理了 ${processedCount} 条工单。`);\r\n\r\n        // 刷新列表\r\n        this.getList();\r\n      }).catch(error => {\r\n        this.batchRejectDialog.loading = false;\r\n        // 错误信息会由全局错误处理器显示\r\n        console.error('批量拒绝操作失败:', error);\r\n      });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  .drawer-footer {\r\n    display: flex;\r\n    padding: 0 50px 20px;\r\n    .el-button {\r\n      flex: 1\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}