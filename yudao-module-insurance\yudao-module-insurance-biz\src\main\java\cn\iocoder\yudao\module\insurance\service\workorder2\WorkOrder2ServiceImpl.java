package cn.iocoder.yudao.module.insurance.service.workorder2;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.irs.core.PdfSigning;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.*;
import cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.*;
import cn.iocoder.yudao.module.insurance.convert.workorder2.WorkOrder2Convert;
import cn.iocoder.yudao.module.insurance.dal.dataobject.batchoperationlog.BatchOperationLogDO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.ecardrecord.PolicyDo;
import cn.iocoder.yudao.module.insurance.dal.dataobject.ecardrecord.PolicyPdfDo;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrder2DO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrderEventDo;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrderPdfDo;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorderelecticketredo.WorkOrderElecTicketRedoDO;
import cn.iocoder.yudao.module.insurance.dal.mysql.ecardrecord.PolicyPdfMapper;
import cn.iocoder.yudao.module.insurance.dal.mysql.workorder2.WorkOrder2Mapper;
import cn.iocoder.yudao.module.insurance.dal.mysql.workorder2.WorkOrderEventMapper;
import cn.iocoder.yudao.module.insurance.dal.mysql.workorder2.WorkOrderPdfMapper;
import cn.iocoder.yudao.module.insurance.dal.mysql.workorderelecticketredo.WorkOrderElecTicketRedoMapper;
import cn.iocoder.yudao.module.insurance.enums.batchoperation.BatchOperationStatusEnum;
import cn.iocoder.yudao.module.insurance.enums.batchoperation.BatchOperationTypeEnum;
import cn.iocoder.yudao.module.insurance.enums.workorder.InsuranceTypeEnum;
import cn.iocoder.yudao.module.insurance.enums.workorder.SupplementaryFileType;
import cn.iocoder.yudao.module.insurance.enums.workorder.WorkOrderStatusEnum;
import cn.iocoder.yudao.module.insurance.enums.workorder.WorkOrderTicketCompleteStatusEnum;
import cn.iocoder.yudao.module.insurance.service.bankcard.BankCardService;
import cn.iocoder.yudao.module.insurance.service.batchoperationlog.BatchOperationLogService;
import cn.iocoder.yudao.module.insurance.service.electronicbill.ElectronicBillService;
import cn.iocoder.yudao.module.insurance.service.permanentresident.PermanentResidentService;
import cn.iocoder.yudao.module.insurance.utils.WorkOrderPdfUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.deepoove.poi.data.RowRenderData;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.insurance.enums.ErrorCodeConstants.*;

/**
 * 工单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class WorkOrder2ServiceImpl implements WorkOrder2Service {

    public static final String ELEC_TICKET_OUT_NET = "223.4.64.54";
    public static final String ELEC_TICKET_INNER_NET = "59.202.38.205:8083";
    @Resource
    private WorkOrder2Mapper workOrder2Mapper;

    @Resource
    private WorkOrderPdfMapper workOrderPdfMapper;

    @Resource
    private WorkOrderEventMapper workOrderEventMapper;

    @Resource
    private ElectronicBillService electronicBillService;

    @Resource
    private FileApi fileApi;

    @Resource
    private PolicyPdfMapper policyPdfMapper;

    @Resource
    private BankCardService bankCardService;

    @Resource
    private PermanentResidentService permanentResidentService;


    @Resource(name="transactionManager")
    private DataSourceTransactionManager transactionManager;

    @Resource
    private WorkOrderElecTicketRedoMapper workOrderElecTicketRedoMapper;

    @Resource
    private BatchOperationLogService batchOperationLogService;

    @Override
    public Long createWorkOrder2(WorkOrder2CreateReqVO createReqVO) {
        // 插入
        WorkOrder2DO workOrder2 = WorkOrder2Convert.INSTANCE.convert(createReqVO);
        workOrder2Mapper.insert(workOrder2);
        // 返回
        return workOrder2.getId();
    }

    @Override
    public void updateWorkOrder2(WorkOrder2UpdateReqVO updateReqVO) {
        // 校验存在
        this.validateWorkOrder2Exists(updateReqVO.getId());
        // 更新
        WorkOrder2DO updateObj = WorkOrder2Convert.INSTANCE.convert(updateReqVO);
        workOrder2Mapper.updateById(updateObj);
    }

    @Override
    public void deleteWorkOrder2(Long id) {
        // 校验存在
        this.validateWorkOrder2Exists(id);
        // 删除
        workOrder2Mapper.deleteById(id);
    }

    private void validateWorkOrder2Exists(Long id) {
        if (workOrder2Mapper.selectById(id) == null) {
            throw exception(WORK_ORDER_NOT_EXISTS);
        }
    }

    @Override
    public WorkOrder2DO getWorkOrder2(Long id) {
        return workOrder2Mapper.selectById(id);
    }

    @Override
    public WorkOrderPdfDo getWorkOrderPdf(Long orderId) {
        return workOrderPdfMapper.selectById(orderId);
    }

    @Override
    public Map<Long, WorkOrderPdfDo> getWorkOrderPdfMap(Collection<Long> orderIds) {
        if (CollUtil.isEmpty(orderIds)) {
            return Collections.emptyMap();
        }
        List<WorkOrderPdfDo> list = workOrderPdfMapper.selectBatchIds(orderIds);
        return CollectionUtils.convertMap(list, WorkOrderPdfDo::getOrderId);
    }

    @Override
    public List<WorkOrder2DO> getWorkOrder2List(Collection<Long> ids) {
        return workOrder2Mapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<WorkOrder2DO> getWorkOrder2Page(WorkOrder2PageReqVO pageReqVO) {
        return workOrder2Mapper.selectPage(pageReqVO);
    }

    @Override
    public List<WorkOrder2DO> getWorkOrder2List(WorkOrder2ExportReqVO exportReqVO) {
        return workOrder2Mapper.selectZyWorkOrderList(exportReqVO);
    }

    @Override
    public List<WorkOrder2ExcelVO> getWorkOrder2List2(WorkOrder2ExportReqVO exportReqVO) {
        return workOrder2Mapper.selectList2(exportReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void takeWorkOrder(Long id) {
        try {
            workOrderStatusUpdateHelper(id, WorkOrderStatusEnum.WAIT_TAKING, WorkOrderStatusEnum.WAIT_PROCESSING);
            WorkOrderPdfDo pdf = workOrderPdfMapper.selectById(id);
            Path tempFile = Files.createTempFile("work_order_pdf", ".pdf");
            File unsignedPdf = HttpUtil.downloadFileFromUrl(pdf.getUnsignedPdf(), tempFile.toFile());
            LocalDate today = LocalDate.now();
            String signedPdfPath = String.format("signedPdf/%d/%d/%d/%s.pdf", today.getYear(), today.getMonthValue(), today.getDayOfMonth(), UUID.fastUUID());
            String signedPdf = fileApi.createFile(signedPdfPath, PdfSigning.createSignPdf(unsignedPdf));
//            String signedPdf = fileApi.createFile(signedPdfPath, Files.readAllBytes(unsignedPdf.toPath()));
            pdf.setSignedPdf(signedPdf);
            workOrderPdfMapper.updateById(pdf);
            Files.deleteIfExists(tempFile);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public void hospitalCheck(Long id) {
        workOrderStatusUpdateHelper(id, WorkOrderStatusEnum.WAIT_HOSPITAL_CHECK, WorkOrderStatusEnum.WAIT_PROCESSING);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void process(Long id) {
        workOrderStatusUpdateHelper(id, WorkOrderStatusEnum.WAIT_PROCESSING, WorkOrderStatusEnum.WAIT_VISITING);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void visit(WorkOrderUpdateInsuranceVo updateInsuranceVo) {
        WorkOrder2DO updateData = WorkOrder2Convert.INSTANCE.convert(updateInsuranceVo);
        // 处理理赔图片信息
        if (updateInsuranceVo.getClaimImages() != null) {
            updateData.setClaimImages(updateInsuranceVo.getClaimImages());
        }
        workOrder2Mapper.updateById(updateData);
        workOrderStatusUpdateHelper(updateInsuranceVo.getId(), WorkOrderStatusEnum.WAIT_VISITING, WorkOrderStatusEnum.FINISHED);
    }

    @Override
    public PageResult<WorkOrder2DO> getWorkOrder2Page(WorkOrder2OpenApiPageReqVO openApiPageReqVO) {
        IPage<WorkOrder2DO> mpPage = MyBatisUtils.buildPage(openApiPageReqVO);
        workOrder2Mapper.selectPage2(mpPage, openApiPageReqVO);
        return new PageResult<>(mpPage.getRecords(), mpPage.getTotal());
    }

    @Override
    public List<WorkOrderEventDo> getWorkOrderEventList(Long workOrderId) {
        return workOrderEventMapper.getWorkOrderEventList(workOrderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectWorkOrder(WorkOrderUpdateInsuranceVo updateInsuranceVo) {
        WorkOrder2DO updateData = WorkOrder2Convert.INSTANCE.convert(updateInsuranceVo);
        workOrder2Mapper.updateById(updateData);
        workOrderStatusUpdateHelper(updateInsuranceVo.getId(), WorkOrderStatusEnum.WAIT_TAKING, WorkOrderStatusEnum.REJECT);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delayWorkOrder(WorkOrderUpdateInsuranceVo updateInsuranceVo) {
        WorkOrder2DO updateData = WorkOrder2Convert.INSTANCE.convert(updateInsuranceVo);
        workOrder2Mapper.updateById(updateData);
        workOrderStatusUpdateHelper(updateInsuranceVo.getId(), WorkOrderStatusEnum.WAIT_TAKING, WorkOrderStatusEnum.DELAY);
    }

    @Transactional
    @Override
    public void compensatingReport(WorkOrderUpdateInsuranceVo workOrderUpdateInsuranceVo) {
        WorkOrder2DO workOrder2DO = getWorkOrder2(workOrderUpdateInsuranceVo.getId());
        if (workOrder2DO == null) {
            throw new ServiceException(WORK_ORDER_NOT_EXISTS);
        }
        if (!Objects.equals(workOrder2DO.getStatus(), WorkOrderStatusEnum.WAIT_VISITING.getStatus())) {
            throw new ServiceException(WORK_ORDER_STATUS_NOT_RIGHT);
        }

        if (workOrderUpdateInsuranceVo.getActualMoney() == null) {
            throw new ServiceException(WORK_ORDER_ACTUAL_MONEY_REQUIRED);
        }
        if (workOrderUpdateInsuranceVo.getCompensatingDate() == null) {
            throw new ServiceException(WORK_ORDER_COMPENSATING_DATE_REQUIRED);
        }

        WorkOrderUpdateInsuranceVo workOrder2UpdateReqVO = new WorkOrderUpdateInsuranceVo();
        workOrder2UpdateReqVO.setId(workOrderUpdateInsuranceVo.getId());
        workOrder2UpdateReqVO.setActualMedicalCosts(workOrderUpdateInsuranceVo.getActualMedicalCosts());
        workOrder2UpdateReqVO.setActualHospitalizationAllowance(workOrderUpdateInsuranceVo.getActualHospitalizationAllowance());
        workOrder2UpdateReqVO.setActualDiagnosisAllowance(workOrderUpdateInsuranceVo.getActualDiagnosisAllowance());
        workOrder2UpdateReqVO.setActualRecoveryAllowance(workOrderUpdateInsuranceVo.getActualRecoveryAllowance());
        workOrder2UpdateReqVO.setActualMoney(workOrderUpdateInsuranceVo.getActualMoney());
        workOrder2UpdateReqVO.setCompensatingDate(workOrderUpdateInsuranceVo.getCompensatingDate());
        workOrder2UpdateReqVO.setRemark(workOrderUpdateInsuranceVo.getRemark());
        // 设置理赔图片信息
        workOrder2UpdateReqVO.setClaimImages(workOrderUpdateInsuranceVo.getClaimImages());
        visit(workOrder2UpdateReqVO);
    }

    @Override
    public String buildUnsignedPdf(WorkOrder2DO workOrder2DO) {
        String unsignedPdfUrl = null;
        if (workOrder2DO.getElectronicBill() == null) {
            return null;
        }
        List<String> electronicBillPdfUrlList = electronicBillService.extractPdfUrlList(workOrder2DO.getElectronicBill());
        List<RowRenderData> chargeDetailsList = electronicBillService.extractChargeDetailsList(workOrder2DO.getElectronicBill());

        List<Path> electronicBillPdfPathList = new ArrayList<>();

        electronicBillPdfUrlList.forEach(electronicBillPdfUrl -> {
            try {
                electronicBillPdfUrl = electronicBillPdfUrl.replace(ELEC_TICKET_OUT_NET, ELEC_TICKET_INNER_NET);
                log.info("开始下载({}-{})电子票据:{}", workOrder2DO.getHospitalCode(), workOrder2DO.getTreatmentSerialNumber(), electronicBillPdfUrl);
                Path electronicBillPdfPath = Files.createTempFile("electronic_bill_pdf", ".pdf");
                HttpUtil.downloadFileFromUrl(electronicBillPdfUrl, electronicBillPdfPath.toFile());
                electronicBillPdfPathList.add(electronicBillPdfPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });

        Path wordPath = null;
        Path pdfPath = null;
        Path mergedPdfPath = null;
        try {
            wordPath = Files.createTempFile("word_order_", ".docx");
            pdfPath = new File(wordPath.getParent().toFile(), wordPath.getFileName().toString().replace(".docx", ".pdf")).toPath();
            mergedPdfPath = Files.createTempFile("word_order_", ".pdf");

            WorkOrder2WordRenderVO model = new WorkOrder2WordRenderVO();
            BeanUtils.copyProperties(workOrder2DO, model);
            model.getDetailTable().setAuxItemList(chargeDetailsList);
            model.getBankCardDetailTable().setBankCardList(bankCardService.getBankCardByIdcard(workOrder2DO.getIdCardNumber())
                    .stream()
                    .map(it -> RowRenderData.build(workOrder2DO.getName(), it.getBankName(), it.getBankCard(), it.getIsDefault()))
                    .collect(Collectors.toList()));
            model.getPersonExtraInfoDetailTable().setPersonExtraInfoList(permanentResidentService.getPersonExtraInfoListByIdcard(workOrder2DO.getIdCardNumber())
                    .stream()
                    .map(it -> RowRenderData.build(it.getAddress(), it.getPhone(), getPersonExtraInfoSourceText(it.getSource())))
                    .collect(Collectors.toList()));
            WorkOrderPdfUtil.render("template/work_order_template.docx", model, wordPath.toString());
            WorkOrderPdfUtil.word2pdf(wordPath.toString(), wordPath.getParent().toString());
            String sourceFile = pdfPath.toString();
            List<String> appendFileList = new ArrayList<>();
            electronicBillPdfPathList.forEach(electronicBillPdfPath -> appendFileList.add(electronicBillPdfPath.toString()));
            String targetFile = mergedPdfPath.toString();
            WorkOrderPdfUtil.merge(sourceFile, appendFileList, targetFile);

            if (Files.exists(mergedPdfPath) && Files.size(mergedPdfPath) > 0) {
                LocalDate today = LocalDate.now();
                String unsignedPdfPath = String.format("unsignedPdf/%d/%d/%d/%s.pdf", today.getYear(), today.getMonthValue(), today.getDayOfMonth(), UUID.fastUUID());
                unsignedPdfUrl = fileApi.createFile(unsignedPdfPath, Files.readAllBytes(mergedPdfPath));
                WorkOrderPdfDo workOrderPdfDo = workOrderPdfMapper.selectById(workOrder2DO.getId());
                if (workOrderPdfDo == null) {
                    workOrderPdfDo = new WorkOrderPdfDo();
                    workOrderPdfDo.setOrderId(workOrder2DO.getId());
                    workOrderPdfDo.setUnsignedPdf(unsignedPdfUrl);
                    workOrderPdfMapper.insert(workOrderPdfDo);
                } else {
                    workOrderPdfDo.setUnsignedPdf(unsignedPdfUrl);
                    workOrderPdfMapper.updateById(workOrderPdfDo);
                }
            }
        } catch (Exception e) {
            log.error("工单{}生成未签章pdf失败", workOrder2DO.getId(), e);
        } finally {
            try {
                if (wordPath != null) {
                    Files.deleteIfExists(wordPath);
                }
                if (pdfPath != null) {
                    Files.deleteIfExists(pdfPath);
                }
                if (mergedPdfPath != null) {
                    Files.deleteIfExists(mergedPdfPath);
                }
                electronicBillPdfPathList.forEach(it -> {
                    try {
                        Files.deleteIfExists(it);
                    } catch (IOException ignored) {}
                });
            } catch (IOException ignored) {}
        }
        return unsignedPdfUrl;
    }

    @Override
    public Long count() {
        return workOrder2Mapper.selectCount();
    }

    @Override
    public Long countWaitAcceptOrder() {
        return workOrder2Mapper.selectCount(WorkOrder2DO::getStatus, WorkOrderStatusEnum.WAIT_TAKING.getStatus());
    }

    @Override
    public Long countWaitHandle() {
        return workOrder2Mapper.selectCount(WorkOrder2DO::getStatus, WorkOrderStatusEnum.WAIT_PROCESSING.getStatus());
    }

    @Override
    public Long countWaitVisiting() {
        return workOrder2Mapper.selectCount(WorkOrder2DO::getStatus, WorkOrderStatusEnum.WAIT_VISITING.getStatus());
    }

    @Override
    public Long countFinished() {
        return workOrder2Mapper.selectCount(WorkOrder2DO::getStatus, WorkOrderStatusEnum.FINISHED.getStatus());
    }

    @Override
    public Long countSumAcceptOrder() {
        return workOrder2Mapper.selectCount(new LambdaQueryWrapperX<WorkOrder2DO>()
                .in(WorkOrder2DO::getStatus,
                        WorkOrderStatusEnum.WAIT_HOSPITAL_CHECK.getStatus(),
                        WorkOrderStatusEnum.WAIT_PROCESSING.getStatus(),
                        WorkOrderStatusEnum.WAIT_VISITING.getStatus(),
                        WorkOrderStatusEnum.FINISHED.getStatus()
                ));
    }

    @Override
    public BigDecimal countCompensatingMoney() {
        return workOrder2Mapper.countCompensatingMoney();
    }

    @Override
    public List<InsuranceCompensatingMoney> countCompensatingMoneyGroupByCompany() {
        return workOrder2Mapper.countCompensatingMoneyGroupByCompany();
    }

    @Override
    public List<CompensatingMonthPersonNumber> countCompensatingPersonNumberGroupByMonth(Integer year) {
        return workOrder2Mapper.countCompensatingPersonNumberGroupByMonth(year);
    }

    @Override
    public List<CompensatingAreaPersonNumber> countCompensatingPersonNumberGroupByArea(Integer year) {
        return workOrder2Mapper.countCompensatingPersonNumberGroupByArea(year);
    }

    @Override
    public PageResult<WorkOrder2RespVO> getWorkOrder2Page2(WorkOrder2PageReqVO pageVO) {
        IPage<WorkOrder2RespVO> mpPage = MyBatisUtils.buildPage(pageVO);
        workOrder2Mapper.selectWorkOrderPage(mpPage, pageVO);
        return new PageResult<>(mpPage.getRecords(), mpPage.getTotal());
    }

    @Override
    public PageResult<CompensatingPersonAndCompany> getCompensatingPersonAndCompanyPage(WorkOrder2PageReqVO pageReqVO) {
        IPage<CompensatingPersonAndCompany> mpPage = MyBatisUtils.buildPage(pageReqVO);
        workOrder2Mapper.selectCompensatingPersonAndCompanyPage(mpPage, pageReqVO);
        return new PageResult<>(mpPage.getRecords(), mpPage.getTotal());
    }

    @Override
    public Long countNoDataOrder() {
        return workOrder2Mapper.selectCount(WorkOrder2DO::getCompleteStatus, WorkOrderTicketCompleteStatusEnum.NO_DATA.getStatus());
    }

    @Override
    public Long countUnsureOrder() {
        return workOrder2Mapper.selectCount(WorkOrder2DO::getCompleteStatus, WorkOrderTicketCompleteStatusEnum.UNSURE.getStatus());
    }

    @Override
    public Long countCompleteOrder() {
        return workOrder2Mapper.selectCount(WorkOrder2DO::getCompleteStatus, WorkOrderTicketCompleteStatusEnum.COMPLETE.getStatus());
    }

    @Override
    public BigDecimal countSuggestCompensatingMoney() {
        return workOrder2Mapper.countSuggestCompensatingMoney();
    }

    @Override
    public List<WorkOrderStatisticsByArea> countWorkOrderGroupByArea() {
        return workOrder2Mapper.countWorkOrderGroupByArea();
    }

    @Override
    public WorkOrderStatisticsByAge countWorkOrderGroupByAge(Integer beginAge, Integer endAge) {
        return workOrder2Mapper.countWorkOrderGroupByAge(beginAge, endAge);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void returnToPrev(WorkOrderUpdateInsuranceVo vo) {
        validateWorkOrder2Exists(vo.getId());
        WorkOrder2DO workOrder = workOrder2Mapper.selectById(vo.getId());
        if (canReturn(workOrder)) {
            returnToStatus(workOrder, WorkOrderStatusEnum.WAIT_TAKING);
        } else {
            throw exception(WORK_ORDER_CAN_NOT_RETURN);
        }
    }

    @Override
    public WorkOrderStatisticsByDiagnosisCodeAndAge countWorkOrderByDiagnosisCodeAndAge(String diagnosisCode, Integer beginAge, Integer endAge) {
        return workOrder2Mapper.countWorkOrderByDiagnosisCodeAndAge(diagnosisCode, beginAge, endAge);
    }

    @Override
    public List<WorkOrderStatisticsByDiagnosisCodeAndArea> countWorkOrderByDiagnosisCodeGroupByArea(String diagnosisCode) {
        return workOrder2Mapper.countWorkOrderByDiagnosisCodeGroupByArea(diagnosisCode);
    }

    @Override
    public List<WorkOrderStatisticsByDiagnosisCodeAndMonth> countWorkOrderByDiagnosisCodeGroupByMonth(String diagnosisCode, Integer year) {
        return workOrder2Mapper.countWorkOrderByDiagnosisCodeGroupByMonth(diagnosisCode, year);
    }

    @Override
    public List<WorkOrderStatisticsByDiagnosisCodeAndSex> countWorkOrderByDiagnosisCodeGroupBySex(String diagnosisCode) {
        return workOrder2Mapper.countWorkOrderByDiagnosisCodeGroupBySex(diagnosisCode);
    }

    @Override
    public List<TopDiagnosis> countTopDiagnosis(Integer top) {
        return workOrder2Mapper.countTopDiagnosis(top);
    }

    @Override
    public void confirm(WorkOrderUpdateInsuranceVo workOrderUpdateInsuranceVo) {
        WorkOrder2DO workOrder2DO = getWorkOrder2(workOrderUpdateInsuranceVo.getId());
        if (workOrder2DO == null) {
            throw new ServiceException(WORK_ORDER_NOT_EXISTS);
        }
        if (!Objects.equals(workOrder2DO.getStatus(), WorkOrderStatusEnum.WAIT_PROCESSING.getStatus())) {
            throw new ServiceException(WORK_ORDER_STATUS_NOT_RIGHT);
        }

        process(workOrder2DO.getId());
    }

    @Override
    public Long countRejected() {
        return workOrder2Mapper.selectCount(WorkOrder2DO::getStatus, WorkOrderStatusEnum.REJECT.getStatus());
    }

    @Override
    public Long countDelay() {
        return workOrder2Mapper.selectCount(WorkOrder2DO::getStatus, WorkOrderStatusEnum.DELAY.getStatus());
    }

    @Override
    public Long countWorkOrder(List<InsuranceTypeEnum> types, WorkOrderStatusEnum workOrderStatusEnum) {
        return workOrder2Mapper.countWorkOrder(types, workOrderStatusEnum.getStatus());
    }

    @Override
    public List<String> getHospitalNameList() {
        return workOrder2Mapper.selectDistinctHospitalNameList();
    }

    @Override
    public List<SettlementWorkOrderImportResultVo> importSettlementWorkOrder(List<SettlementWorkOrderImportVo> workOrder2ImportExportExcelVOList) {
        List<SettlementWorkOrderImportResultVo> settlementWorkOrderImportResultVos = new LinkedList<>();
        workOrder2ImportExportExcelVOList.forEach(workOrder2ImportExportExcelVO -> {
            SettlementWorkOrderImportResultVo settlementWorkOrderImportResultVo = new SettlementWorkOrderImportResultVo();
            settlementWorkOrderImportResultVo.setIdCard(workOrder2ImportExportExcelVO.getIdCard());
            settlementWorkOrderImportResultVo.setName(workOrder2ImportExportExcelVO.getName());
            settlementWorkOrderImportResultVo.setType(workOrder2ImportExportExcelVO.getType());
            settlementWorkOrderImportResultVo.setTreatmentTime(workOrder2ImportExportExcelVO.getTreatmentTime());
            settlementWorkOrderImportResultVo.setInHospitalTime(workOrder2ImportExportExcelVO.getInHospitalTime());
            settlementWorkOrderImportResultVo.setOutHospitalTime(workOrder2ImportExportExcelVO.getOutHospitalTime());
            settlementWorkOrderImportResultVo.setHospitalName(workOrder2ImportExportExcelVO.getHospitalName());
            List<WorkOrder2DO> workOrder2DOList;
            if ("门诊".equals(workOrder2ImportExportExcelVO.getType())) {
                if (workOrder2ImportExportExcelVO.getTreatmentTime() == null) {
                    settlementWorkOrderImportResultVo.setResult("门诊类型工单的就诊时间不能为空");
                    settlementWorkOrderImportResultVos.add(settlementWorkOrderImportResultVo);
                    return;
                }
                workOrder2DOList = workOrder2Mapper.selectMzWorkOrderList(workOrder2ImportExportExcelVO.getIdCard(), workOrder2ImportExportExcelVO.getTreatmentTime());
            } else if ("住院".equals(workOrder2ImportExportExcelVO.getType())) {
                if (workOrder2ImportExportExcelVO.getInHospitalTime() == null || workOrder2ImportExportExcelVO.getOutHospitalTime() == null) {
                    settlementWorkOrderImportResultVo.setResult("住院类型工单的入院时间或出院时间不能为空");
                    settlementWorkOrderImportResultVos.add(settlementWorkOrderImportResultVo);
                    return;
                }
                workOrder2DOList = workOrder2Mapper.selectZyWorkOrderList(workOrder2ImportExportExcelVO.getIdCard(), workOrder2ImportExportExcelVO.getInHospitalTime(), workOrder2ImportExportExcelVO.getOutHospitalTime());
            } else {
                settlementWorkOrderImportResultVo.setResult("工单类型不正确,只能填门诊或住院");
                settlementWorkOrderImportResultVos.add(settlementWorkOrderImportResultVo);
                return;
            }
            if (workOrder2DOList.isEmpty()) {
                settlementWorkOrderImportResultVo.setResult("未查询到工单记录");
            } else if (workOrder2DOList.size() > 1) {
                settlementWorkOrderImportResultVo.setResult("工单记录数量大于1");
            } else {
                WorkOrder2DO workOrder2DO = workOrder2DOList.get(0);
                if (!workOrder2DO.getStatus().equals(WorkOrderStatusEnum.WAIT_TAKING.getStatus())) {
                    if (workOrder2DO.getStatus().equals(WorkOrderStatusEnum.FINISHED.getStatus())) {
                        settlementWorkOrderImportResultVo.setResult("工单已经是完成状态");
                    } else {
                        settlementWorkOrderImportResultVo.setResult("工单已接单处理或者已延后或者已拒绝，请手动处理");
                    }
                } else {
                    settleWorkOrder(workOrder2ImportExportExcelVO, workOrder2DO, settlementWorkOrderImportResultVo);
                }
            }

            settlementWorkOrderImportResultVos.add(settlementWorkOrderImportResultVo);
        });
        return settlementWorkOrderImportResultVos;
    }

    @Override
    public void createPdf(Long id) {
        WorkOrder2DO workOrder2DO = getWorkOrder2(id);
        WorkOrderPdfDo pdf = workOrderPdfMapper.selectOne(WorkOrderPdfDo::getOrderId, workOrder2DO.getId());
        if (pdf != null) {
            throw new ServiceException(500, String.format("工单（%d）pdf已存在, 无需创建", workOrder2DO.getId()));
        }
        buildUnsignedPdf(workOrder2DO);
    }

    @Override
    public List<WorkOrder2DO> getWorkOrder2ListWithoutElecTicket() {
        return workOrder2Mapper.getWorkOrder2ListWithoutElecTicket();
    }

    @Override
    public void updateWorkOrder2(WorkOrder2DO workOrder2DO) {
        workOrder2Mapper.updateById(workOrder2DO);
    }

    @Override
    public List<WorkOrderStatRespVo> getWorkOrderStat(WorkOrderStatReqVo reqVo) {
        return workOrder2Mapper.getWorkOrderStat(reqVo);
    }

    @Override
    public void addWorkOrderSupplementaryInfo(Long workOrderId, List<String> fileUrls) {
        // 获取工单
        WorkOrder2DO workOrder = workOrder2Mapper.selectById(workOrderId);
        if (workOrder == null) {
            throw new ServiceException(500, "工单不存在");
        }
        workOrder.setSupplementaryFiles(JsonUtils.toJsonString(fileUrls));
        workOrder.setUpdateTime(new Date());

        // 保存更新
        workOrder2Mapper.updateById(workOrder);
    }

    @Override
    public void addWorkOrderSupplementaryInfo(Long workOrderId, SupplementaryFileType type, String url) {
        // 获取工单
        WorkOrder2DO workOrder = workOrder2Mapper.selectById(workOrderId);
        if (workOrder == null) {
            throw new ServiceException(500, "工单不存在");
        }
        Map<SupplementaryFileType, List<String>> supplementaryFiles = workOrder.getSupplementaryFiles() == null? new HashMap<>(): JsonUtils.parseObject(workOrder.getSupplementaryFiles(), new TypeReference<Map<SupplementaryFileType, List<String>>>() {});
        List<String> urls = supplementaryFiles.get(type) == null ? new ArrayList<>(): supplementaryFiles.get(type);
        urls.add(url);
        supplementaryFiles.put(type, urls);
        workOrder.setSupplementaryFiles(JsonUtils.toJsonString(supplementaryFiles));
        workOrder.setUpdateTime(new Date());
        workOrder2Mapper.updateById(workOrder);
    }

    @Override
    public void delWorkOrderSupplementaryInfo(Long workOrderId, SupplementaryFileType type, String fileUrl) {
        WorkOrder2DO workOrder = workOrder2Mapper.selectById(workOrderId);
        if (workOrder == null) {
            throw new ServiceException(500, "工单不存在");
        }
        Map<SupplementaryFileType, List<String>> supplementaryFiles = JsonUtils.parseObject(workOrder.getSupplementaryFiles(), new TypeReference<Map<SupplementaryFileType, List<String>>>() {});
        supplementaryFiles.get(type).remove(fileUrl);
        if (supplementaryFiles.get(type).isEmpty()) {
            supplementaryFiles.remove(type);
        }
        workOrder.setSupplementaryFiles(JsonUtils.toJsonString(supplementaryFiles));
        workOrder.setUpdateTime(new Date());
        workOrder2Mapper.updateById(workOrder);
    }

    @Override
    public List<ClaimAmountImportResultVO> importClaimAmountWorkOrder(List<ClaimAmountImportVO> claimAmountImportVOList) {
        List<ClaimAmountImportResultVO> resultList = new ArrayList<>();
        for (ClaimAmountImportVO vo : claimAmountImportVOList) {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW); // 开启新事务
            TransactionStatus status = transactionManager.getTransaction(def);
            ClaimAmountImportResultVO resultVO = new ClaimAmountImportResultVO();
            try {
                resultVO.setIdentifier(vo.getIdentifier());
                resultVO.setElderName(vo.getElderName());
                resultVO.setClaimAmount(vo.getClaimAmount());
                resultVO.setPaymentTime(vo.getPaymentTime());
                resultVO.setHospital(vo.getHospital());
                resultVO.setVisitTime(vo.getVisitTime());
                resultVO.setRemark(vo.getRemark());

                // 查询工单
                WorkOrder2DO workOrder = getWorkOrder2(vo.getIdentifier());
                if (workOrder == null) {
                    resultVO.setResult("未找到对应工单");
                    resultList.add(resultVO);
                    transactionManager.commit(status); // 提交事务
                    continue;
                }

                // 检查工单状态是否为已完成
                if (Objects.equals(workOrder.getStatus(), WorkOrderStatusEnum.FINISHED.getStatus())) {
                    resultVO.setResult("工单已是完成状态，无需处理");
                    resultList.add(resultVO);
                    transactionManager.commit(status); // 提交事务
                    continue;
                }

                // 更新工单信息
                workOrder.setActualMoney(vo.getClaimAmount());
                workOrder.setCompensatingDate(vo.getPaymentTime());
                workOrder.setRemark(vo.getRemark());
                workOrder.setStatus(WorkOrderStatusEnum.FINISHED.getStatus());
                workOrder2Mapper.updateById(workOrder);

                // 记录事件
                WorkOrderEventDo event = new WorkOrderEventDo();
                event.setWorkOrderId(workOrder.getId());
                event.setType(WorkOrderStatusEnum.FINISHED.getStatus());
                workOrderEventMapper.insert(event);

                resultVO.setResult("处理成功");
                transactionManager.commit(status); // 提交事务
            } catch (Exception e) {
                log.error("处理工单标识符 {} 时发生异常: {}", vo.getIdentifier(), e.getMessage(), e);
                resultVO.setResult("处理失败：" + e.getMessage());
                transactionManager.rollback(status); // 回滚事务
            }
            resultList.add(resultVO);
        }
        return resultList;
    }

    private void settleWorkOrder(SettlementWorkOrderImportVo workOrder2ImportExportExcelVO, WorkOrder2DO workOrder2DO, SettlementWorkOrderImportResultVo settlementWorkOrderImportResultVo) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW); // 事物隔离级别，开启新事务，这样会比较安全些。
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            takeWorkOrder(workOrder2DO.getId());
            process(workOrder2DO.getId());
            WorkOrderUpdateInsuranceVo workOrderUpdateInsuranceVo = convertToWorkOrderUpdateInsuranceVo(workOrder2ImportExportExcelVO, workOrder2DO);
            visit(workOrderUpdateInsuranceVo);
            settlementWorkOrderImportResultVo.setResult("处理成功");
            transactionManager.commit(status);
        } catch (Exception e) {
            log.error("导入完成工单处理失败, 身份证号：{}， 入院时间：{}， 出院时间：{}，异常原因：{}", workOrder2ImportExportExcelVO.getIdCard(), workOrder2ImportExportExcelVO.getInHospitalTime(), workOrder2ImportExportExcelVO.getOutHospitalTime(), e.getMessage(), e);
            settlementWorkOrderImportResultVo.setResult(String.format("处理失败，异常原因：%s", e.getMessage()));
            transactionManager.rollback(status);
        }

    }

    @NotNull
    private static WorkOrderUpdateInsuranceVo convertToWorkOrderUpdateInsuranceVo(SettlementWorkOrderImportVo workOrder2ImportExportExcelVO, WorkOrder2DO workOrder2DO) {
        WorkOrderUpdateInsuranceVo workOrderUpdateInsuranceVo = new WorkOrderUpdateInsuranceVo();
        workOrderUpdateInsuranceVo.setId(workOrder2DO.getId());
        workOrderUpdateInsuranceVo.setCompensatingDate(new Date());
        workOrderUpdateInsuranceVo.setActualMedicalCosts(workOrder2ImportExportExcelVO.getActualMedicalCosts());
        workOrderUpdateInsuranceVo.setActualHospitalizationAllowance(workOrder2ImportExportExcelVO.getActualHospitalizationAllowance());
        workOrderUpdateInsuranceVo.setActualDiagnosisAllowance(workOrder2ImportExportExcelVO.getActualDiagnosisAllowance());
        workOrderUpdateInsuranceVo.setActualMoney(workOrder2ImportExportExcelVO.getActualMoney());
        workOrderUpdateInsuranceVo.setActualRecoveryAllowance(workOrder2ImportExportExcelVO.getActualRecoveryAllowance());
        workOrderUpdateInsuranceVo.setCompensatingDate(workOrder2ImportExportExcelVO.getCompensatingTime());
        return workOrderUpdateInsuranceVo;
    }

    private boolean canReturn(WorkOrder2DO workOrder) {
        return WorkOrderStatusEnum.WAIT_PROCESSING.getStatus().equals(workOrder.getStatus())
                || WorkOrderStatusEnum.WAIT_VISITING.getStatus().equals(workOrder.getStatus())
                || WorkOrderStatusEnum.DELAY.getStatus().equals(workOrder.getStatus());
    }

    private void returnToStatus(WorkOrder2DO workOrder, WorkOrderStatusEnum statusEnum) {
        workOrder.setStatus(statusEnum.getStatus());
        workOrder2Mapper.updateById(workOrder);
        WorkOrderEventDo workOrderEventDo = new WorkOrderEventDo();
        workOrderEventDo.setWorkOrderId(workOrder.getId());
        workOrderEventDo.setType(10);
        workOrderEventMapper.insert(workOrderEventDo);
    }

    @Override
    public String buildInsuancePolicy(String idCard, String phone, String address) {
        PolicyPdfDo policyPdfDo = policyPdfMapper.selectOneByIdcard(idCard);
        if (policyPdfDo != null) {
            return policyPdfDo.getPdfUrl();
        }
        Path wordPath = null;
        Path pdfPath = null;
        String insurancePolicyUrl = null;
        try {
            wordPath = Files.createTempFile("word_order_insurance_policy", ".docx");
            pdfPath = new File(wordPath.getParent().toFile(), wordPath.getFileName().toString().replace(".docx", ".pdf")).toPath();
            PolicyDo model = new PolicyDo();
            model.setBirthday(IdcardUtil.getBirth(idCard));
            model.setPhone(phone);
            model.setAddress(address);
            model.setIdcard(idCard);
            WorkOrderPdfUtil.render("template/insurance_policy_template.docx", model, wordPath.toString());
            WorkOrderPdfUtil.word2pdf(wordPath.toString(), wordPath.getParent().toString());
            LocalDate today = LocalDate.now();
            String insurancePolicyPath = String.format("unsignedPdf/%d/%d/%d/%s.pdf", today.getYear(), today.getMonthValue(), today.getDayOfMonth(), UUID.fastUUID());
            insurancePolicyUrl = fileApi.createFile(insurancePolicyPath, Files.readAllBytes(pdfPath));
            policyPdfDo = new PolicyPdfDo();
            policyPdfDo.setIdcard(idCard);
            policyPdfDo.setPdfUrl(insurancePolicyUrl);
            policyPdfMapper.insert(policyPdfDo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (wordPath != null) {
                    Files.deleteIfExists(wordPath);
                }
                if (pdfPath != null) {
                    Files.deleteIfExists(pdfPath);
                }
            } catch (IOException ignored) {}
        }
        return insurancePolicyUrl;
    }

    @Override
    public Long createWorkOrderDo(WorkOrder2DO workOrder) {
        workOrder2Mapper.insert(workOrder);
        // 返回
        return workOrder.getId();
    }

    private void workOrderStatusUpdateHelper(Long id, WorkOrderStatusEnum checkStatus, WorkOrderStatusEnum setStatus) {
        // 校验存在
        this.validateWorkOrder2Exists(id);
        WorkOrder2DO workOrder = workOrder2Mapper.selectById(id);
        if (!checkStatus.getStatus().equals(workOrder.getStatus())) {
            ServiceExceptionUtil.exception(WORK_ORDER_STATUS_NOT_RIGHT);
        }
        WorkOrderEventDo workOrderEventDo = new WorkOrderEventDo();
        workOrderEventDo.setWorkOrderId(id);
        workOrderEventDo.setType(setStatus.getStatus());
        workOrderEventMapper.insert(workOrderEventDo);
        workOrder.setStatus(setStatus.getStatus());
        workOrder2Mapper.updateById(workOrder);
    }

    public String getPersonExtraInfoSourceText(Integer source) {
        String sourceText;
        switch (source) {
            case 1:
                sourceText = "常驻人口库";
                break;
            case 2:
                sourceText = "省公安厅";
                break;
            case 3:
                sourceText = "医疗数据";
                break;
            case 4:
                sourceText = "健康码";
                break;
            default:
                sourceText = "";
                break;
        }
        return sourceText;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<WandaDataImportResultVO> importWandaData(List<WandaDataImportVO> dataList) {
        List<WandaDataImportResultVO> resultList = new ArrayList<>();
        
        for (WandaDataImportVO data : dataList) {
            // 为每条记录创建独立事务
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            TransactionStatus status = transactionManager.getTransaction(def);
            
            try {
                // 查询工单
                List<WorkOrder2DO> workOrders = workOrder2Mapper.selectList(
                    new LambdaQueryWrapperX<WorkOrder2DO>()
                        .eq(WorkOrder2DO::getIdCardNumber, data.getIdCardNumber())
                        .eq(WorkOrder2DO::getTreatmentSerialNumber, data.getLsh())
                );
                
                if (CollUtil.isEmpty(workOrders)) {
                    resultList.add(new WandaDataImportResultVO(data.getIdCardNumber(), data.getLsh(), 
                        "失败", "未找到对应工单"));
                    transactionManager.commit(status);
                    continue;
                }
                
                boolean hasUpdated = false;
                for (WorkOrder2DO workOrder : workOrders) {
                    // 跳过已完成的工单
                    if (Objects.equals(workOrder.getStatus(), WorkOrderStatusEnum.FINISHED.getStatus())) {
                        continue;
                    }
                    
                    // 处理电子票据ID
                    String electronicBillIds = processElectronicBillIds(data, workOrder);
                    
                    // 更新工单信息
                    workOrder.setMainDiagnosisCode(data.getMainDiagnosisCode());
                    if (StringUtils.isNotBlank(data.getMainDiagnosisCode())) {
                        workOrder.setMainDiagnosisCode(data.getMainDiagnosisCode());
                    }
                    if (StringUtils.isNotBlank(data.getMainDiagnosisName())) {
                        workOrder.setMainDiagnosisName(data.getMainDiagnosisName());
                    }
                    if (StringUtils.isNotBlank(data.getMainComplaint())) {
                        workOrder.setMainComplaint(data.getMainComplaint());
                    }
                    if (StringUtils.isNotBlank(data.getCurrentMedicalHistory())) {
                        workOrder.setCurrentMedicalHistory(data.getCurrentMedicalHistory());
                    }
                    if (StringUtils.isNotBlank(data.getPastMedicalHistory())) {
                        workOrder.setPastMedicalHistory(data.getPastMedicalHistory());
                    }
                    if (StringUtils.isNotBlank(data.getGeneticHistory())) {
                        workOrder.setGeneticHistory(data.getGeneticHistory());
                    }
                    if (StringUtils.isNotBlank(data.getLeaveHospitalDescription())) {
                        workOrder.setLeaveHospitalDescription(data.getLeaveHospitalDescription());
                    }
                    if (StringUtils.isNotBlank(data.getLeaveHospitalDiagnosisName())) {
                        workOrder.setLeaveHospitalDiagnosisName(data.getLeaveHospitalDiagnosisName());
                    }
                    if (StringUtils.isNotBlank(data.getInHospitalDiagnosisName())) {
                        workOrder.setInHospitalDiagnosisName(data.getInHospitalDiagnosisName());
                    }
                    if (electronicBillIds != null) {
                        workOrder.setElectronicBillIds(electronicBillIds);
                        if (!Objects.equals(workOrder.getStatus(), WorkOrderStatusEnum.WAIT_TAKING.getStatus())) {
                            //如果是其他状态，退为待接单
                            workOrder.setStatus(WorkOrderStatusEnum.WAIT_TAKING.getStatus());
                            //生成event事件
                            WorkOrderEventDo workOrderEventDo = new WorkOrderEventDo();
                            workOrderEventDo.setWorkOrderId(workOrder.getId());
                            workOrderEventDo.setType(WorkOrderStatusEnum.WAIT_TAKING.getStatus());
                            workOrderEventMapper.insert(workOrderEventDo);
                        }
                    }
                    
                    workOrder2Mapper.updateById(workOrder);
                    
                    // 处理重做记录
                    handleRedoRecord(workOrder, electronicBillIds);
                    hasUpdated = true;
                }
                
                resultList.add(new WandaDataImportResultVO(data.getIdCardNumber(), data.getLsh(), 
                    hasUpdated ? "成功" : "跳过", hasUpdated ? null : "所有工单已完成"));
                
                transactionManager.commit(status);
            } catch (Exception e) {
                transactionManager.rollback(status);
                log.error("处理万达数据失败, 身份证号: {}, 流水号: {}", data.getIdCardNumber(), data.getLsh(), e);
                resultList.add(new WandaDataImportResultVO(data.getIdCardNumber(), data.getLsh(), 
                    "失败", e.getMessage()));
            }
        }
        
        return resultList;
    }

    private String processElectronicBillIds(WandaDataImportVO data, WorkOrder2DO workOrder) {
        // 清理并获取新的票据ID
        String newIds = cleanElectronicBillIds(data.getElectronicBillIds());
        if (StringUtils.isBlank(newIds)) {
            newIds = cleanElectronicBillIds(data.getOldElectronicBillIds());
        }
        
        // 如果没有新的票据ID，保持原值
        if (StringUtils.isBlank(newIds)) {
            return null;
        }
        
        // 合并现有票据ID
        if (StringUtils.isNotBlank(workOrder.getElectronicBillIds())) {
            Set<String> idSet = new HashSet<>();
            idSet.addAll(Arrays.asList(workOrder.getElectronicBillIds().split(",")));
            idSet.addAll(Arrays.asList(newIds.split(",")));
            return String.join(",", idSet);
        }
        
        return newIds;
    }

    private String cleanElectronicBillIds(String ids) {
        if (StringUtils.isBlank(ids)) {
            return null;
        }
        
        return Arrays.stream(ids.split(","))
            .map(String::trim)
            .filter(id -> !id.matches("[\\s*\\-_0]+"))
            .collect(Collectors.joining(","));
    }

    private void handleRedoRecord(WorkOrder2DO workOrder, String electronicBillIds) {
        WorkOrderElecTicketRedoDO redo = workOrderElecTicketRedoMapper.selectById(workOrder.getId());
        
        if (redo != null) {
            if (StringUtils.isNotBlank(electronicBillIds)) {
                redo.setStatus(1); // 已生成票据ID
                workOrderElecTicketRedoMapper.updateById(redo);
            }
        } else {
            redo = new WorkOrderElecTicketRedoDO();
            redo.setId(workOrder.getId());
            redo.setHospitalCode(workOrder.getHospitalCode());
            redo.setTreatmentSerialNumberType(workOrder.getTreatmentSerialNumberType());
            redo.setTreatmentSerialNumber(workOrder.getTreatmentSerialNumber());
            redo.setName(workOrder.getName());
            redo.setIdCardNumber(workOrder.getIdCardNumber());
            redo.setTreatmentDatetime(workOrder.getTreatmentDatetime());
            redo.setStatus(StringUtils.isNotBlank(electronicBillIds) ? 1 : 0);
            redo.setType(workOrder.getType());
            workOrderElecTicketRedoMapper.insert(redo);
        }
    }

    @Override
    public List<HospitalDataStatisticsVO> getHospitalDataStatistics() {
        return workOrder2Mapper.getHospitalDataStatistics();
    }

    @Override
    public HospitalDetailStatisticsVO getHospitalDetailStatistics(String hospitalName) {
        return workOrder2Mapper.getHospitalDetailStatistics(hospitalName);
    }

    @Override
    public boolean existsWorkOrder(String idCardNumber, String hospitalCode, String treatmentSerialNumber, Integer treatmentSerialNumberType) {
        return workOrder2Mapper.existsWorkOrder(idCardNumber, hospitalCode, treatmentSerialNumber, treatmentSerialNumberType);
    }

    @Override
    public WorkOrder2DO getWorkOrderByKey(String idCardNumber, String hospitalCode, String treatmentSerialNumber, Integer treatmentSerialNumberType) {
        return workOrder2Mapper.selectOne(new LambdaQueryWrapperX<WorkOrder2DO>()
                .eq(WorkOrder2DO::getIdCardNumber, idCardNumber)
                .eq(WorkOrder2DO::getHospitalCode, hospitalCode)
                .eq(WorkOrder2DO::getTreatmentSerialNumber, treatmentSerialNumber)
                .eq(WorkOrder2DO::getTreatmentSerialNumberType, treatmentSerialNumberType));
    }

    @Override
    public WorkOrder2DO getWorkOrderByKeyAndType(String idCardNumber, String hospitalCode, String treatmentSerialNumber, Integer treatmentSerialNumberType, Integer type) {
        return workOrder2Mapper.selectOne(new LambdaQueryWrapperX<WorkOrder2DO>()
                .eq(WorkOrder2DO::getIdCardNumber, idCardNumber)
                .eq(WorkOrder2DO::getHospitalCode, hospitalCode)
                .eq(WorkOrder2DO::getTreatmentSerialNumber, treatmentSerialNumber)
                .eq(WorkOrder2DO::getTreatmentSerialNumberType, treatmentSerialNumberType)
                .eq(WorkOrder2DO::getType, type));
    }

    @Override
    public WorkOrder2DO getWorkOrder2BySupplementaryFileRecordId(Long supplementaryFileRecordId) {
        return workOrder2Mapper.selectWorkOrder2BySupplementaryFileRecordId(supplementaryFileRecordId);
    }

    // Authorization-aware count methods implementation
    @Override
    public Long countWithAuth() {
        return workOrder2Mapper.countWithAuth();
    }

    @Override
    public Long countWaitAcceptOrderWithAuth() {
        return workOrder2Mapper.countWaitAcceptOrderWithAuth();
    }

    @Override
    public Long countWaitHandleWithAuth() {
        return workOrder2Mapper.countWaitHandleWithAuth();
    }

    @Override
    public Long countWaitVisitingWithAuth() {
        return workOrder2Mapper.countWaitVisitingWithAuth();
    }

    @Override
    public Long countFinishedWithAuth() {
        return workOrder2Mapper.countFinishedWithAuth();
    }

    @Override
    public Long countRejectedWithAuth() {
        return workOrder2Mapper.countRejectedWithAuth();
    }

    @Override
    public Long countDelayWithAuth() {
        return workOrder2Mapper.countDelayWithAuth();
    }

    @Override
    public Long countSumAcceptOrderWithAuth() {
        return workOrder2Mapper.countSumAcceptOrderWithAuth();
    }

    @Override
    public Long countNoDataOrderWithAuth() {
        return workOrder2Mapper.countNoDataOrderWithAuth();
    }

    @Override
    public Long countUnsureOrderWithAuth() {
        return workOrder2Mapper.countUnsureOrderWithAuth();
    }

    @Override
    public Long countCompleteOrderWithAuth() {
        return workOrder2Mapper.countCompleteOrderWithAuth();
    }

    @Override
    public Long countWorkOrderWithAuth(List<InsuranceTypeEnum> types, WorkOrderStatusEnum workOrderStatusEnum) {
        List<Integer> typeCodes = types.stream().map(InsuranceTypeEnum::getCode).collect(Collectors.toList());
        return workOrder2Mapper.countWorkOrderWithAuth(typeCodes, workOrderStatusEnum.getStatus());
    }

    @Override
    public List<String> getHospitalNameListWithAuth() {
        return workOrder2Mapper.selectDistinctHospitalNameListWithAuth();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchRejectRespVO batchRejectWorkOrders(Date cutoffDate) {
        // 生成唯一批次号
        String batchId = UUID.fastUUID().toString();
        Date startTime = new Date();

        // 创建批量操作日志记录
        BatchOperationLogDO batchLog = BatchOperationLogDO.builder()
                .batchId(batchId)
                .operationType(BatchOperationTypeEnum.BATCH_REJECT.getCode())
                .status(BatchOperationStatusEnum.RUNNING.getCode())
                .parameters(JsonUtils.toJsonString(Collections.singletonMap("cutoffDate", cutoffDate)))
                .processedCount(0)
                .operatorId(SecurityFrameworkUtils.getLoginUserId() != null ? SecurityFrameworkUtils.getLoginUserId().toString() : null)
                .startTime(startTime)
                .build();

        batchOperationLogService.createBatchOperationLog(batchLog);

        try {
            // 查询符合条件的工单：状态为待接单(0)且就医时间小于等于截止日期（只查询必要字段）
            List<WorkOrder2DO> workOrdersToReject = workOrder2Mapper.selectWorkOrdersForBatchReject(cutoffDate);

            int processedCount = workOrdersToReject.size();

            if (processedCount > 0) {
                // 提取工单ID列表
                List<Long> workOrderIds = workOrdersToReject.stream()
                    .map(WorkOrder2DO::getId)
                    .collect(Collectors.toList());

                // 批量更新工单状态（只更新必要字段）
                workOrder2Mapper.batchUpdateWorkOrdersToReject(workOrderIds, batchId);

                // 批量创建工单事件记录
                for (Long workOrderId : workOrderIds) {
                    WorkOrderEventDo workOrderEvent = new WorkOrderEventDo();
                    workOrderEvent.setWorkOrderId(workOrderId);
                    workOrderEvent.setType(WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus());
                    workOrderEventMapper.insert(workOrderEvent);
                }
            }

            // 更新批量操作日志为完成状态
            batchLog.setStatus(BatchOperationStatusEnum.COMPLETED.getCode());
            batchLog.setProcessedCount(processedCount);
            batchLog.setEndTime(new Date());
            batchOperationLogService.updateBatchOperationLog(batchLog);

            // 返回结果
            BatchRejectRespVO respVO = new BatchRejectRespVO();
            respVO.setBatchId(batchId);
            respVO.setProcessedCount(processedCount);
            return respVO;

        } catch (Exception e) {
            // 更新批量操作日志为失败状态
            batchLog.setStatus(BatchOperationStatusEnum.FAILED.getCode());
            batchLog.setEndTime(new Date());
            batchLog.setRemarks("批量拒绝操作失败: " + e.getMessage());
            batchOperationLogService.updateBatchOperationLog(batchLog);

            throw new ServiceException(500, "批量拒绝工单操作失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchRecoverRespVO batchRecoverWorkOrders(String batchOperationId) {
        // 验证批次操作是否存在且类型正确
        BatchOperationLogDO originalBatchLog = batchOperationLogService.getBatchOperationLogByBatchId(batchOperationId);
        if (originalBatchLog == null) {
            throw new ServiceException(500, "批次操作不存在: " + batchOperationId);
        }

        if (!BatchOperationTypeEnum.BATCH_REJECT.getCode().equals(originalBatchLog.getOperationType())) {
            throw new ServiceException(500, "只能恢复批量拒绝操作，当前批次操作类型为: " + originalBatchLog.getOperationType());
        }

        // 生成新的批次号用于恢复操作
        String recoverBatchId = UUID.fastUUID().toString();
        Date startTime = new Date();

        // 创建恢复操作的批量操作日志记录
        BatchOperationLogDO recoverBatchLog = BatchOperationLogDO.builder()
                .batchId(recoverBatchId)
                .operationType(BatchOperationTypeEnum.BATCH_RECOVER.getCode())
                .status(BatchOperationStatusEnum.RUNNING.getCode())
                .parameters(JsonUtils.toJsonString(Collections.singletonMap("originalBatchId", batchOperationId)))
                .processedCount(0)
                .operatorId(SecurityFrameworkUtils.getLoginUserId() != null ? SecurityFrameworkUtils.getLoginUserId().toString() : null)
                .startTime(startTime)
                .remarks("恢复批次操作: " + batchOperationId)
                .build();

        batchOperationLogService.createBatchOperationLog(recoverBatchLog);

        try {
            // 查询需要恢复的工单：batch_operation_id匹配且状态为行政拒绝(7)（只查询必要字段）
            List<WorkOrder2DO> workOrdersToRecover = workOrder2Mapper.selectWorkOrdersForBatchRecover(batchOperationId);

            // 过滤出有原始状态的工单
            List<WorkOrder2DO> validWorkOrders = workOrdersToRecover.stream()
                .filter(workOrder -> workOrder.getOriginalStatus() != null)
                .collect(Collectors.toList());

            int processedCount = validWorkOrders.size();

            if (processedCount > 0) {
                // 提取工单ID列表
                List<Long> workOrderIds = validWorkOrders.stream()
                    .map(WorkOrder2DO::getId)
                    .collect(Collectors.toList());

                // 批量恢复工单状态（只更新必要字段）
                workOrder2Mapper.batchRecoverWorkOrders(workOrderIds);

                // 批量创建工单事件记录
                for (WorkOrder2DO workOrder : validWorkOrders) {
                    WorkOrderEventDo workOrderEvent = new WorkOrderEventDo();
                    workOrderEvent.setWorkOrderId(workOrder.getId());
                    workOrderEvent.setType(workOrder.getOriginalStatus()); // 恢复后的状态
                    workOrderEventMapper.insert(workOrderEvent);
                }
            }

            // 更新恢复操作日志为完成状态
            recoverBatchLog.setStatus(BatchOperationStatusEnum.COMPLETED.getCode());
            recoverBatchLog.setProcessedCount(processedCount);
            recoverBatchLog.setEndTime(new Date());
            batchOperationLogService.updateBatchOperationLog(recoverBatchLog);

            // 返回结果
            BatchRecoverRespVO respVO = new BatchRecoverRespVO();
            respVO.setBatchId(batchOperationId); // 返回被恢复的原始批次号
            respVO.setProcessedCount(processedCount);
            return respVO;

        } catch (Exception e) {
            // 更新恢复操作日志为失败状态
            recoverBatchLog.setStatus(BatchOperationStatusEnum.FAILED.getCode());
            recoverBatchLog.setEndTime(new Date());
            recoverBatchLog.setRemarks("批量恢复操作失败: " + e.getMessage());
            batchOperationLogService.updateBatchOperationLog(recoverBatchLog);

            throw new ServiceException(500, "批量恢复工单操作失败: " + e.getMessage());
        }
    }
}
