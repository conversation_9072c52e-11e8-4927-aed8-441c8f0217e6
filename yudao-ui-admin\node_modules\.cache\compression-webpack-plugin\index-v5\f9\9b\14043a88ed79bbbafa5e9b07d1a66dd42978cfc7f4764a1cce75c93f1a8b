
0d73ef5c60e3f66b0b2170965e170dcc0b03f3e6	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.cf999f423d8ba0672054.hot-update.js\",\"contentHash\":\"82cf5e5d922264bbd7b56c057699e202\"}","integrity":"sha512-sNswoXNWlfVEqvrR1siwhn4YTkcQ2E3nbZILj4glr1UcfHkzQjIqlxfr6Bx8SKMfJbQwEytMIYjp73BstS337Q==","time":1754286885567,"size":86134}