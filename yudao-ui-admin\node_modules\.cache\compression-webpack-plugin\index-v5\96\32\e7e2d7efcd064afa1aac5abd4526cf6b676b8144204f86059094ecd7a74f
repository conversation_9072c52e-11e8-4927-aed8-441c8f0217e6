
c6097edcdc03375bf81a2d7123707d9923ec8839	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"6726bfd1aa0e705765944d8110f35c9e\"}","integrity":"sha512-W4v7jdGTN8kcrF+lZrjw4+Qb+OWmoG0QGpbNjO9sNPLFdvucW9afRcyPn4kZ0WdyUa4S7cgehheqZBTMq3za8w==","time":1754286886776,"size":16754198}