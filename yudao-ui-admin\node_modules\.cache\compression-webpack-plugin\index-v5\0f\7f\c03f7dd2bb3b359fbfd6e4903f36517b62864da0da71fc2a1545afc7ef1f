
261c2321791ed9f917ac052dc76570c9b049a083	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"da2fe5e8d9df0c28184d9242f82ad235\"}","integrity":"sha512-150hBMzWdqKKWuCJhZFO0y+8TY8h6f73f4tJaKFHFfXluvxdVM6JP8UuNsrZqkMSD8exksDVQ1rjYALsBKYzEQ==","time":1754286815909,"size":16741829}