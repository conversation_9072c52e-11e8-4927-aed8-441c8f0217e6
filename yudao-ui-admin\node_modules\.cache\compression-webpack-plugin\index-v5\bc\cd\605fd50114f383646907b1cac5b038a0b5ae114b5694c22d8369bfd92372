
77724a2f025ad4b7053ed1005597851cdcb84ff5	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.71e1968989146e6ac40d.hot-update.js\",\"contentHash\":\"0934274adb0c448f232dbc8ccad19cb7\"}","integrity":"sha512-E94CdJNEAsxmsioWBeDpj7Vj9MkSDT2rYv8Os0ZajuPZsmTjq3MgYOL6nM2sZA8H11JzduPMf/W+SoAQW7kl5A==","time":1754286814268,"size":9551}