
381102cab607dc3726b64ab15557f367d48a2a15	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"228690aa5df84163faf1152c1110a993\"}","integrity":"sha512-r8t+A/6Vu7oQMqDv2SfZBPyePL8nbhGCnGr7lFW9QIaAdeJGbSl5ThpZedEA3wd+WV+RXUd/TLes5Xackm4h9Q==","time":1754286870838,"size":3474321}